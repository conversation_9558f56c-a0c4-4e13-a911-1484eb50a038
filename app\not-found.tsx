// components/NotFound.js
import { Home } from "lucide-react";
import Link from "next/link";
import React from "react";

const NotFound = () => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-red-600 mb-2">
          404 - Not Found
        </h1>
        <p className="text-gray-600">
          The page you are looking for does not exist.
        </p>
        <Link
          href="/"
          className="font-medium my-8 gap-2 self-center max-w-fit mx-auto rounded-full px-6 py-2 flex items-center justify-center bg-slate-900 text-white"
        >
          <Home className="h-5 w-5" />
          Go Home
        </Link>
        <div className="mt-4  text-center">
          <img
            src="/not-found.svg "
            className="h-80"
            alt="Not Found Illustration"
          />
        </div>
      </div>
    </div>
  );
};

export default NotFound;
