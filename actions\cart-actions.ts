"use server";

import { Product } from "@/data/product";

export interface AddToCartParams {
  productId: number;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  size?: string;
  color?: string;
}

/**
 * Server action to add a product to the cart
 * This is a placeholder for a real server action that would interact with a database
 * In a real application, this would validate the product, check inventory, etc.
 */
export async function addToCart(product: AddToCartParams) {
  try {
    // In a real app, you would:
    // 1. Validate the product exists
    // 2. Check inventory
    // 3. Maybe reserve the inventory for a short time
    // 4. Store the cart in a database if the user is logged in
    
    // For now, we'll just return success with the product data
    // The actual cart management happens on the client side with CartContext
    return {
      success: true,
      message: `Added ${product.name} to cart`,
      product: {
        id: `${product.productId}-${product.size || 'default'}-${product.color || 'default'}-${Date.now()}`,
        productId: product.productId.toString(),
        name: product.name,
        price: product.price,
        quantity: product.quantity,
        image: product.image,
        size: product.size,
        color: product.color
      }
    };
  } catch (error) {
    console.error("Error adding to cart:", error);
    return {
      success: false,
      message: "Failed to add product to cart",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Server action to add a product to the wishlist
 */
export async function addToWishlist(product: { 
  productId: number; 
  name: string; 
  price: number; 
  image?: string; 
}) {
  try {
    // In a real app, you would store this in a database
    return {
      success: true,
      message: `Added ${product.name} to wishlist`,
      product: {
        id: `${product.productId}-${Date.now()}`,
        productId: product.productId.toString(),
        name: product.name,
        price: product.price,
        image: product.image,
        dateAdded: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error("Error adding to wishlist:", error);
    return {
      success: false,
      message: "Failed to add product to wishlist",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Server action to validate a coupon code
 */
export async function validateCoupon(code: string, subtotal: number) {
  try {
    // In a real app, you would check a database for valid coupons
    const validCoupons = [
      { code: 'WELCOME10', discountType: 'percentage', discountValue: 10, minOrderValue: 0 },
      { code: 'SAVE20', discountType: 'percentage', discountValue: 20, minOrderValue: 50 },
      { code: 'FLAT15', discountType: 'fixed', discountValue: 15, minOrderValue: 100 }
    ];
    
    const coupon = validCoupons.find(c => c.code === code);
    
    if (!coupon) {
      return {
        success: false,
        message: "Invalid coupon code"
      };
    }
    
    if (subtotal < coupon.minOrderValue) {
      return {
        success: false,
        message: `This coupon requires a minimum order of $${coupon.minOrderValue}`
      };
    }
    
    return {
      success: true,
      message: coupon.discountType === 'percentage' 
        ? `Coupon applied! ${coupon.discountValue}% off your order` 
        : `Coupon applied! $${coupon.discountValue} off your order`,
      coupon: {
        ...coupon,
        isApplied: true,
        expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      }
    };
  } catch (error) {
    console.error("Error validating coupon:", error);
    return {
      success: false,
      message: "Failed to validate coupon",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
