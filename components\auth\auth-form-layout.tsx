import React from "react";
import Image from "next/image";

interface AuthFormLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

export function AuthFormLayout({
  children,
  title,
  subtitle,
}: AuthFormLayoutProps) {
  return (
    <div className="w-full">
      {/* Form Section - Takes left half and scrolls naturally */}
      <div className="w-1/2 min-h-screen p-8 lg:p-12">
        <div className="mx-auto max-w-md space-y-6">
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              {title}
            </h1>
            {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
          </div>

          {children}

          {/* Add some padding at the bottom to ensure proper scrolling space */}
          <div className="h-20" />
        </div>
      </div>

      {/* Image Section - Fixed to the right half */}
      <div className="fixed right-0 top-0 w-1/2 h-screen">
        <div className="relative h-full w-full bg-gradient-to-br from-blue-600 to-purple-700">
          <Image
            src="/hero.png"
            alt="Authentication"
            fill
            className="object-cover opacity-20"
            priority
          />
          <div className="absolute inset-0 bg-black/20" />

          {/* Overlay content */}
          <div className="relative flex h-full items-center justify-center p-12">
            <div className="text-center text-white space-y-6">
              <h2 className="text-4xl font-bold">Welcome to Our Platform</h2>
              <p className="text-xl text-white/90 max-w-md">
                Join thousands of users who trust our secure and reliable
                platform
              </p>
              <div className="flex items-center justify-center space-x-8 text-white/80">
                <div className="text-center">
                  <div className="text-2xl font-bold">10K+</div>
                  <div className="text-sm">Active Users</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">99.9%</div>
                  <div className="text-sm">Uptime</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">24/7</div>
                  <div className="text-sm">Support</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
