"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/lib/auth-context";
import { ShieldAlert } from "lucide-react";

export default function UnauthorizedPage() {
    const { user, signOut } = useAuth();

    return (
        <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
            <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-6 shadow-lg">
                <div className="flex flex-col items-center justify-center text-center">
                    <div className="mb-4 rounded-full bg-red-100 p-3">
                        <ShieldAlert className="h-6 w-6 text-red-600" />
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight">Access Denied</h1>
                    <p className="mt-2 text-gray-600">
                        You don&apos;t have permission to access this page.
                    </p>
                </div>
                <div className="space-y-4">
                    {user ? (
                        <>
                            <p className="text-sm text-gray-500">
                                Your current role ({user.role}) doesn&apos;t have the required permissions.
                                Please contact an administrator if you believe this is an error.
                            </p>
                            <div className="flex flex-col space-y-2">
                                <Link href="/">
                                    <Button variant="default" className="w-full">
                                        Go Home
                                    </Button>
                                </Link>
                                <Button
                                    variant="outline"
                                    className="w-full"
                                    onClick={() => signOut()}
                                >
                                    Sign Out
                                </Button>
                            </div>
                        </>
                    ) : (
                        <>
                            <p className="text-sm text-gray-500">
                                You need to sign in with appropriate credentials to access this page.
                            </p>
                            <div className="flex flex-col space-y-2">
                                <Link href="/auth/sign-in">
                                    <Button variant="default" className="w-full">
                                        Sign In
                                    </Button>
                                </Link>
                                <Link href="/">
                                    <Button variant="outline" className="w-full">
                                        Go Home
                                    </Button>
                                </Link>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}