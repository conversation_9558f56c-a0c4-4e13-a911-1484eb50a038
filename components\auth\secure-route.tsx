"use client";

import { useAuth } from "@/lib/auth-context";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";
import { Loader2 } from "lucide-react";

interface SecureRouteProps {
    children: React.ReactNode;
    fallbackUrl?: string;
    roles?: string[];
}

export function SecureRoute({
    children,
    fallbackUrl = "/auth/sign-in",
    roles = []
}: SecureRouteProps) {
    const { user, isLoading } = useAuth();
    const router = useRouter();
    const pathname = usePathname();

    useEffect(() => {
        if (!isLoading && !user) {
            // Store the current URL to redirect back after login
            sessionStorage.setItem("redirectAfterLogin", pathname);
            router.push(`${fallbackUrl}?redirect=${encodeURIComponent(pathname)}`);
        }

        if (!isLoading && user && roles.length > 0) {
            // Check if the user has required role
            const hasRequiredRole = roles.includes(user.role);
            if (!hasRequiredRole) {
                router.push("/unauthorized");
            }
        }
    }, [user, isLoading, router, pathname, fallbackUrl, roles]);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-lg">Loading...</span>
            </div>
        );
    }

    // If not loading and we have a user, or if we're loading still
    if (user) {
        if (roles.length === 0 || roles.includes(user.role)) {
            return <>{children}</>;
        }
    }

    // This shouldn't render as the useEffect should redirect,
    // but it's here as a fallback
    return null;
}