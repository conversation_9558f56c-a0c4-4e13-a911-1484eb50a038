"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { AuthFormLayout } from "@/components/auth/auth-form-layout";
import { AuthInput } from "@/components/auth/auth-input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { useAuth } from "@/lib/auth-context";

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const { forgotPassword } = useAuth();

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const success = await forgotPassword(email);

      if (success) {
        setIsSubmitted(true);
      } else {
        setError("Failed to send reset instructions. Please try again later.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again later.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AuthFormLayout
      imageUrl="https://assets.lummi.ai/assets/QmajeMPWFjh8Uv5GXZfCMSF8JgJUnLudBNc5QitXCf8UzR?auto=format&w=1500"
      testimonialText="The customer support team was incredibly helpful when I needed to reset my password. The process was simple and quick!"
      testimonialAuthor="Alex Rivera"
      testimonialRole="Lead Designer at CreativeStudio"
    >
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">
          Reset your password
        </h1>
        <p className="mt-2 text-sm text-muted-foreground">
          Enter your email address and we'll send you instructions to reset your
          password
        </p>
      </div>
      {isSubmitted ? (
        <div className="space-y-6">
          <Alert className="border-green-500/50 bg-green-500/10 text-green-700">
            <CheckCircle2 className="h-4 w-4" />
            <AlertDescription>
              Password reset instructions have been sent to{" "}
              <strong>{email}</strong>. Please check your inbox.
            </AlertDescription>
          </Alert>
          <div className="text-center">
            <Link
              href="/auth/sign-in"
              className="text-sm font-medium text-primary hover:underline"
            >
              Back to sign in
            </Link>
          </div>
        </div>
      ) : (
        <>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-5">
            {" "}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <AuthInput
                id="email"
                placeholder="<EMAIL>"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setEmail(e.target.value)
                }
              />
            </div>{" "}
            <Button
              type="submit"
              className="w-full h-11 text-base font-medium rounded-xl shadow-sm hover:shadow-md transition-all"
              disabled={isLoading}
            >
              {isLoading
                ? "Sending instructions..."
                : "Send reset instructions"}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm">
            Remember your password?{" "}
            <Link
              href="/auth/sign-in"
              className="font-medium text-primary hover:underline"
            >
              Sign in
            </Link>
          </div>
        </>
      )}
    </AuthFormLayout>
  );
}
