export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  icon: string;
  color: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
  productCount: number;
  featured: boolean;
}

export const categories: Category[] = [
  {
    id: "1",
    name: "Running Shoes",
    slug: "running-shoes",
    description: "Premium performance shoes engineered for speed and endurance. Perfect for runners who demand the best in comfort and performance.",
    image: "/656.png",
    icon: "running",
    color: {
      primary: "indigo-600",
      secondary: "indigo-500",
      accent: "indigo-100",
      background: "indigo-50",
    },
    productCount: 24,
    featured: true,
  },
  {
    id: "2",
    name: "Casual Sneakers",
    slug: "casual-sneakers",
    description: "Stylish comfort for everyday wear with premium materials. The perfect blend of fashion and functionality for your daily adventures.",
    image: "/651.webp",
    icon: "casual",
    color: {
      primary: "rose-600",
      secondary: "rose-500",
      accent: "rose-100",
      background: "rose-50",
    },
    productCount: 36,
    featured: true,
  },
  {
    id: "3",
    name: "<PERSON> Shoes",
    slug: "basketball-shoes",
    description: "Court-ready performance with enhanced grip and support. Designed for athletes who need maximum performance on the court.",
    image: "/822.png",
    icon: "basketball",
    color: {
      primary: "amber-600",
      secondary: "amber-500",
      accent: "amber-100",
      background: "amber-50",
    },
    productCount: 18,
    featured: true,
  },
  {
    id: "4",
    name: "Training Shoes",
    slug: "training-shoes",
    description: "Versatile support for intense workouts and cross-training. Built to handle any workout you can throw at them.",
    image: "/564.webp",
    icon: "training",
    color: {
      primary: "emerald-600",
      secondary: "emerald-500",
      accent: "emerald-100",
      background: "emerald-50",
    },
    productCount: 29,
    featured: true,
  },
  {
    id: "5",
    name: "Lifestyle",
    slug: "lifestyle",
    description: "Fashion-forward designs for the style-conscious individual. Where comfort meets contemporary design.",
    image: "/642.webp",
    icon: "lifestyle",
    color: {
      primary: "purple-600",
      secondary: "purple-500",
      accent: "purple-100",
      background: "purple-50",
    },
    productCount: 15,
    featured: false,
  },
  {
    id: "6",
    name: "Outdoor",
    slug: "outdoor",
    description: "Rugged durability for outdoor adventures. Built to withstand the elements while keeping you comfortable.",
    image: "/564.webp",
    icon: "outdoor",
    color: {
      primary: "green-600",
      secondary: "green-500",
      accent: "green-100",
      background: "green-50",
    },
    productCount: 12,
    featured: false,
  },
];

// Helper function to get category by slug
export const getCategoryBySlug = (slug: string): Category | undefined => {
  return categories.find(category => category.slug === slug);
};

// Helper function to get featured categories
export const getFeaturedCategories = (): Category[] => {
  return categories.filter(category => category.featured);
};

// Helper function to get all categories
export const getAllCategories = (): Category[] => {
  return categories;
};
