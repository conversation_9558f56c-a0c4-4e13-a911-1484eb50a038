import * as React from "react";
const Logos = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={1244}
    height={146}
    fill="none"
    className="absolute -bottom-28 mx-auto "
  >
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M150 78c0-112 547-29 547-78"
    />
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M423 78c0-64 274-29 274-78"
    />
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M559 78c0-61 138-29 138-78"
    />
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M1243 78c0-112-547-29-547-78"
    />
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M1108 78c3-82-412-29-412-78"
    />
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M970 78c0-64-274-29-274-78M697 3v75"
    />
    <path
      stroke="#CBD5E1"
      strokeDasharray="6 6"
      d="M834 78c0-61-138-29-138-78"
    />
    <path
      stroke="#FB923C"
      strokeDasharray="6 6"
      d="M285 78C282-4 697 49 697 0"
    />
    <rect width={65} height={65} x={0.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={0.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <path
      fill="#F4B401"
      d="M53.8 90.266H12.2a2.667 2.667 0 0 0-2.667 2.666v35.2a2.667 2.667 0 0 0 2.667 2.667h41.6a2.667 2.667 0 0 0 2.667-2.667v-35.2a2.667 2.667 0 0 0-2.667-2.666Z"
    />
    <path fill="#1BA261" d="M52.2 93.999H13.8v33.067h38.4V93.999Z" />
    <path
      fill="#fff"
      fillOpacity={0.396}
      fillRule="evenodd"
      d="M41.268 113.2c-2.821 0-5.156 1.043-5.544 2.401h-.057v2.407H46.87v-2.407h-.056c-.389-1.358-2.723-2.401-5.545-2.401Zm0-1.6a2.4 2.4 0 1 0 0-4.801 2.4 2.4 0 0 0 0 4.801ZM24.734 113.2c-2.821 0-5.156 1.043-5.544 2.401h-.057v2.407h11.203v-2.407h-.057c-.388-1.358-2.723-2.401-5.545-2.401Zm0-1.6a2.4 2.4 0 1 0 0-4.801 2.4 2.4 0 0 0 0 4.801Z"
      clipRule="evenodd"
    />
    <path
      fill="#F7F7F7"
      fillRule="evenodd"
      d="M33 111.599c-3.762 0-6.873 1.391-7.391 3.2h-.076v3.209h14.934v-3.209h-.076c-.517-1.809-3.63-3.2-7.391-3.2Zm0-2.134a3.2 3.2 0 1 0 0-6.4 3.2 3.2 0 0 0 0 6.4Z"
      clipRule="evenodd"
    />
    <path
      fill="url(#a)"
      fillOpacity={0.6}
      fillRule="evenodd"
      d="m37.267 126.532 4.266 4.267h9.6l-4.266-4.267h-9.6Z"
      clipRule="evenodd"
    />
    <g filter="url(#b)">
      <path fill="#F1F1F1" d="M46.867 124.399h-9.6v2.134h9.6v-2.134Z" />
    </g>
    <rect width={67} height={67} x={136.5} y={77.032} fill="#fff" rx={15.5} />
    <rect
      width={67}
      height={67}
      x={136.5}
      y={77.032}
      stroke="#FB923C"
      strokeWidth={3}
      rx={15.5}
    />
    <g filter="url(#c)">
      <path
        fill="#FFCF48"
        fillRule="evenodd"
        d="M161.962 89.732h16l15.467 27.734h-16l-15.467-27.734Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#d)">
      <path
        fill="#4587F4"
        fillRule="evenodd"
        d="M154.571 130.266c2.319-4.307 7.467-12.8 7.467-12.8h31.467l-8.001 13.866h-30.933s-.17-.751 0-1.066Z"
        clipRule="evenodd"
      />
    </g>
    <path
      fill="#1BA261"
      fillRule="evenodd"
      d="m161.962 89.732 8 14.4-15.467 27.2-8-13.866 15.467-27.734Z"
      clipRule="evenodd"
    />
    <path
      fill="url(#e)"
      fillRule="evenodd"
      d="m162.37 117.495 6.763 13.292h-14.311l7.548-13.292Z"
      clipRule="evenodd"
    />
    <path
      fill="url(#f)"
      fillRule="evenodd"
      d="m162.37 117.495 6.763 13.292h-14.311l7.548-13.292Z"
      clipRule="evenodd"
    />
    <path
      fill="url(#g)"
      fillRule="evenodd"
      d="m161.977 89.75 4.647 20.242 3.363-5.879-8.01-14.363Z"
      clipRule="evenodd"
    />
    <rect width={65} height={65} x={274.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={274.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <path
      fill="#4C8BF5"
      d="M323.8 89.999h-33.6a3.733 3.733 0 0 0-3.733 3.733v33.6a3.733 3.733 0 0 0 3.733 3.734h33.6a3.733 3.733 0 0 0 3.733-3.734v-33.6A3.733 3.733 0 0 0 323.8 90Z"
    />
    <mask
      id="h"
      width={42}
      height={43}
      x={286}
      y={89}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "alpha",
      }}
    >
      <path
        fill="#fff"
        d="M323.8 89.999h-33.6a3.733 3.733 0 0 0-3.733 3.733v33.6a3.733 3.733 0 0 0 3.733 3.734h33.6a3.733 3.733 0 0 0 3.733-3.734v-33.6A3.733 3.733 0 0 0 323.8 90Z"
      />
    </mask>
    <g mask="url(#h)">
      <path
        fill="url(#i)"
        fillRule="evenodd"
        d="m295.787 122.332 10.68 10.061h21.749l.384-22.66-10.102-10.721-.582 18.428-13.007 4.892h-9.122Z"
        clipRule="evenodd"
      />
      <g filter="url(#j)">
        <path
          fill="#000"
          fillOpacity={0.01}
          d="M323.8 89.999h-33.6a3.733 3.733 0 0 0-3.733 3.733v33.6a3.733 3.733 0 0 0 3.733 3.734h33.6a3.733 3.733 0 0 0 3.733-3.734v-33.6A3.733 3.733 0 0 0 323.8 90Z"
        />
      </g>
    </g>
    <mask
      id="k"
      width={42}
      height={43}
      x={286}
      y={89}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "alpha",
      }}
    >
      <path
        fill="#fff"
        d="M323.8 89.999h-33.6a3.733 3.733 0 0 0-3.733 3.733v33.6a3.733 3.733 0 0 0 3.733 3.734h33.6a3.733 3.733 0 0 0 3.733-3.734v-33.6A3.733 3.733 0 0 0 323.8 90Z"
      />
    </mask>
    <g mask="url(#k)">
      <g filter="url(#l)">
        <path
          fill="#F5F5F5"
          fillRule="evenodd"
          d="M307 126.799c8.984 0 16.267-7.283 16.267-16.267 0-8.984-7.283-16.266-16.267-16.266-8.984 0-16.267 7.282-16.267 16.266 0 8.984 7.283 16.267 16.267 16.267Z"
          clipRule="evenodd"
        />
      </g>
      <path
        fill="#4C8BF5"
        fillRule="evenodd"
        d="M304.299 102.331c-2.161.723-4.002 2.304-5.027 4.304a8.452 8.452 0 0 0-.773 2.183 8.533 8.533 0 0 0 .768 5.61 8.774 8.774 0 0 0 2.409 2.896 8.915 8.915 0 0 0 3.224 1.591c1.479.387 3.053.378 4.541.046 1.345-.303 2.619-.932 3.635-1.846 1.073-.966 1.841-2.234 2.247-3.601.443-1.488.501-3.078.224-4.608h-8.348v3.386h4.837a4.08 4.08 0 0 1-1.774 2.682 5.152 5.152 0 0 1-1.956.755 5.981 5.981 0 0 1-2.118-.004 5.315 5.315 0 0 1-1.975-.835 5.382 5.382 0 0 1-2.032-2.663 5.154 5.154 0 0 1 0-3.376 5.396 5.396 0 0 1 1.288-2.048 5.302 5.302 0 0 1 2.652-1.441 5.418 5.418 0 0 1 2.605.103c.715.21 1.374.597 1.913 1.1l1.631-1.595c.286-.284.587-.559.863-.853a8.677 8.677 0 0 0-2.857-1.73 9.138 9.138 0 0 0-5.977-.056Z"
        clipRule="evenodd"
      />
      <path
        fill="#A4C4FA"
        fillRule="evenodd"
        d="M323.267 96.4a2.133 2.133 0 1 0 0-4.267 2.133 2.133 0 0 0 0 4.267Zm-32.534 0a2.134 2.134 0 1 0 0-4.269 2.134 2.134 0 0 0 0 4.269Zm32.534 32.533a2.133 2.133 0 1 0 0-4.266 2.133 2.133 0 0 0 0 4.266Zm-32.534 0a2.134 2.134 0 1 0 0-4.268 2.134 2.134 0 0 0 0 4.268Z"
        clipRule="evenodd"
      />
    </g>
    <rect width={65} height={65} x={411.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={411.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <path
      fill="url(#m)"
      fillRule="evenodd"
      d="M423.731 97.544c-1.175 0-2.131.971-2.131 2.17v27.048c0 1.196.954 2.17 2.131 2.17h40.538c1.175 0 2.131-.972 2.131-2.17V99.713c0-1.196-.954-2.17-2.131-2.17h-7.92a7.419 7.419 0 0 1-1.056-.78c-1.954-1.733-2.246-2.752-2.246-2.752-.445-1.038-1.761-1.88-2.937-1.88H444v2.707-2.706h-6.11c-1.176 0-2.492.841-2.937 1.879 0 0-.292 1.019-2.246 2.753a7.419 7.419 0 0 1-1.056.78h-7.92Z"
      clipRule="evenodd"
    />
    <path
      fill="#CBD5E1"
      fillRule="evenodd"
      d="M453.047 94.01s.292 1.02 2.246 2.754c.342.304.699.561 1.056.78h7.92c1.177 0 2.131.973 2.131 2.17v27.048c0 1.198-.956 2.17-2.131 2.17h-40.538c-1.177 0-2.131-.974-2.131-2.17V99.713c0-1.198.956-2.17 2.131-2.17h7.92a7.419 7.419 0 0 0 1.056-.78c1.954-1.733 2.246-2.752 2.246-2.752.445-1.038 1.761-1.88 2.937-1.88h12.22c1.176 0 2.492.842 2.937 1.88Zm-2.937-2.878c.814 0 1.621.286 2.281.708.658.42 1.255 1.032 1.575 1.777l.024.057.011.037a3.152 3.152 0 0 0 .266.468c.263.392.766 1.018 1.689 1.837.222.196.452.371.685.528h7.628c1.747 0 3.131 1.439 3.131 3.17v27.048c0 1.734-1.388 3.17-3.131 3.17h-40.538c-1.747 0-3.131-1.44-3.131-3.17V99.713c0-1.734 1.388-3.17 3.131-3.17h7.628c.233-.156.463-.33.685-.527.923-.82 1.426-1.445 1.689-1.837a3.152 3.152 0 0 0 .266-.468l.011-.037.024-.057c.32-.745.917-1.357 1.575-1.777.66-.422 1.467-.708 2.281-.708h12.22Z"
      clipRule="evenodd"
    />
    <g filter="url(#n)">
      <path
        fill="url(#o)"
        d="M425.867 96.132a.8.8 0 0 1 .8-.8h2.133a.8.8 0 0 1 .8.8v1.333h-3.733v-1.333Z"
      />
    </g>
    <g filter="url(#p)">
      <path
        fill="url(#q)"
        fillRule="evenodd"
        d="M454.133 105.465a1.067 1.067 0 1 0 .001-2.133 1.067 1.067 0 0 0-.001 2.133Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#r)">
      <path fill="#fff" d="M466.4 100.665h-44.8v.533h44.8v-.533Z" />
    </g>
    <g filter="url(#s)">
      <path fill="#fff" d="M466.4 124.665h-44.8v.533h44.8v-.533Z" />
    </g>
    <g filter="url(#t)">
      <path
        fill="#fff"
        fillRule="evenodd"
        d="M453.933 112.931c0 5.486-4.447 9.934-9.933 9.934s-9.933-4.448-9.933-9.934c0-5.486 4.447-9.933 9.933-9.933s9.933 4.447 9.933 9.933ZM444 121.865c4.934 0 8.933-4 8.933-8.934a8.933 8.933 0 0 0-17.866 0 8.933 8.933 0 0 0 8.933 8.934Z"
        clipRule="evenodd"
      />
    </g>
    <rect width={65} height={65} x={548.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={548.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <path
      fill="url(#u)"
      fillRule="evenodd"
      d="M581 113.527c.189.23 7.275 8.805 13.972 8.805 6.789 0 10.632-6.835 10.632-6.835 2.645-4.742.3-9.384.3-9.384s-3.394-7.382-10.932-7.382c-7.43 0-13.793 8.662-13.972 8.908-.179-.246-6.542-8.908-13.972-8.908-7.537 0-10.932 7.382-10.932 7.382s-2.345 4.642.3 9.384c0 0 3.843 6.835 10.632 6.835 6.697 0 13.783-8.575 13.972-8.805Zm-13.687-10.674c-4.643 0-6.619 3.778-6.619 3.778s-2.124 3.384 0 7.73c2.124 4.347 6.545 3.952 6.545 3.952s2.405.345 6.257-2.97c1.335-1.149 2.843-2.738 4.486-4.933 0 0-6.026-7.557-10.669-7.557Zm27.639 0c4.643 0 6.619 3.778 6.619 3.778s2.124 3.384 0 7.73c-2.124 4.347-6.545 3.952-6.545 3.952s-2.405.345-6.257-2.97c-1.335-1.149-2.843-2.738-4.485-4.933 0 0 6.025-7.557 10.668-7.557Z"
      clipRule="evenodd"
    />
    <rect width={65} height={65} x={685.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={685.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <g filter="url(#v)">
      <path
        fill="#C4C4C4"
        d="M733.473 90.268h-30.934a3.2 3.2 0 0 0-3.2 3.2v4.8a3.2 3.2 0 0 0 3.2 3.2h30.934a3.2 3.2 0 0 0 3.2-3.2v-4.8a3.2 3.2 0 0 0-3.2-3.2Z"
      />
    </g>
    <path
      fill="url(#w)"
      fillRule="evenodd"
      d="m706.773 94.63 4.002 4.18h4.179l-5.96-6.489-2.221 2.309Z"
      clipRule="evenodd"
    />
    <g filter="url(#x)">
      <path
        fill="#EFEFEF"
        fillRule="evenodd"
        d="M707.872 95.067a1.6 1.6 0 1 0 0-3.2 1.6 1.6 0 0 0 0 3.2Z"
        clipRule="evenodd"
      />
    </g>
    <path
      fill="url(#y)"
      fillRule="evenodd"
      d="m727.039 94.63 4.003 4.18h4.179l-5.961-6.489-2.221 2.309Z"
      clipRule="evenodd"
    />
    <g filter="url(#z)">
      <path
        fill="#EFEFEF"
        fillRule="evenodd"
        d="M728.139 95.067a1.6 1.6 0 1 0 0-3.2 1.6 1.6 0 0 0 0 3.2Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#A)">
      <path
        fill="#4687F4"
        fillRule="evenodd"
        d="M738.871 99.868c.269-1.767-.98-3.2-2.792-3.2h-36.158c-1.81 0-3.062 1.433-2.792 3.2l2.12 13.865-1.148 13.863c-.27 1.768 2.086 3.2 3.897 3.2h32.191c1.812 0 3.628-1.433 3.358-3.2l-.796-13.863 2.12-13.865Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#B)">
      <path
        fill="#3866D0"
        fillRule="evenodd"
        d="M699.921 96.668h36.158c1.812 0 3.061 1.433 2.792 3.2l-2.12 13.867h-37.502l-2.12-13.867c-.27-1.767.982-3.2 2.792-3.2Z"
        clipRule="evenodd"
      />
    </g>
    <path
      fill="url(#C)"
      fillRule="evenodd"
      d="M734.19 130.796c1.812 0 3.628-1.433 3.358-3.2l-.803-13.6h-37.514l16.199 16.8h18.76Z"
      clipRule="evenodd"
    />
    <path
      fill="#EEE"
      fillRule="evenodd"
      d="m720.597 107.121 6.959-2.66v18.399h-2.148v-15.426l-4.811 1.791v-2.104Zm-10.414 5.427h2.082c1.057 0 1.806-.268 2.246-.804.44-.536.659-1.283.659-2.241 0-1.023-.25-1.8-.75-2.333-.5-.532-1.239-.798-2.215-.798-.928 0-1.673.277-2.234.829-.561.553-.841 1.287-.841 2.204h-2.288l-.024-.074c-.04-1.35.442-2.498 1.447-3.444 1.004-.945 2.318-1.418 3.94-1.418 1.63 0 2.929.44 3.898 1.32.968.88 1.452 2.135 1.452 3.763 0 .737-.22 1.472-.66 2.204-.439.733-1.099 1.292-1.979 1.676 1.057.352 1.806.905 2.246 1.658.44.753.659 1.596.659 2.529 0 1.629-.526 2.91-1.579 3.843-1.053.933-2.399 1.4-4.037 1.4-1.606 0-2.936-.44-3.989-1.32-1.053-.88-1.559-2.073-1.519-3.579l.036-.074h2.276c0 .941.284 1.69.853 2.247.569.557 1.35.835 2.343.835 1.008 0 1.799-.278 2.372-.835.573-.557.86-1.379.86-2.468 0-1.105-.254-1.919-.763-2.443-.508-.524-1.311-.786-2.409-.786h-2.082v-1.891Z"
      clipRule="evenodd"
    />
    <path
      fill="#D4D4D4"
      fillRule="evenodd"
      d="M715.654 113.735h-5.471v-1.187h2.082c1.057 0 1.806-.268 2.246-.804.439-.536.659-1.283.659-2.241 0-1.023-.25-1.8-.75-2.333-.501-.532-1.239-.798-2.215-.798-.928 0-1.673.277-2.234.829-.561.553-.841 1.287-.841 2.204h-2.288l-.024-.074c-.04-1.35.442-2.498 1.446-3.444 1.005-.945 2.319-1.418 3.941-1.418 1.63 0 2.929.44 3.897 1.32.969.88 1.453 2.135 1.453 3.763 0 .737-.22 1.472-.66 2.204-.44.733-1.099 1.292-1.979 1.676.266.089.511.19.738.303Zm11.902-.009v-9.265l-6.959 2.66v2.104l4.811-1.791v6.292h2.148Z"
      clipRule="evenodd"
    />
    <g filter="url(#D)">
      <path
        fill="#000"
        fillOpacity={0.01}
        fillRule="evenodd"
        d="M701.999 130.796h32.191c1.812 0 3.628-1.433 3.358-3.2l-.796-13.867H699.25l-1.148 13.867c-.27 1.767 2.086 3.2 3.897 3.2Z"
        clipRule="evenodd"
      />
    </g>
    <rect width={65} height={65} x={822.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={822.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <path
      fill="url(#E)"
      fillRule="evenodd"
      d="M855 137.199c14.728 0 26.667-11.94 26.667-26.667 0-14.728-11.939-26.667-26.667-26.667-14.728 0-26.667 11.94-26.667 26.667 0 14.727 11.939 26.667 26.667 26.667Z"
      clipRule="evenodd"
    />
    <path
      fill="#fff"
      fillRule="evenodd"
      d="m837.4 123.599 5.067 3.2 6.4-10.933 9.333 5.6 8.267-14.934 5.866 3.467-2.133-15.467-14.4 5.867 5.6 3.2-5.333 9.6-9.334-5.6-9.333 16Z"
      clipRule="evenodd"
    />
    <rect width={65} height={65} x={959.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={959.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <path
      fill="url(#F)"
      fillRule="evenodd"
      d="M983.102 121.727s3.19-3.522 8.507-3.522c5.316 0 8.911 3.522 8.911 3.522l-8.709 9.525-8.709-9.525Zm-4.867-4.699s5.611-5.622 13.576-5.674c7.965-.053 13.739 5.674 13.739 5.674l5.05-5.675s-8.1-7.512-18.789-7.46c-10.686.053-18.83 7.776-18.83 7.776l5.254 5.359Zm-9.562-10.56s10.445-9.247 23.138-9.247c12.689 0 23.199 9.247 23.199 9.247l5.05-5.517s-12.46-11.19-28.27-11.138c-15.814.052-27.846 11.296-27.846 11.296l4.729 5.359Z"
      clipRule="evenodd"
    />
    <rect width={65} height={65} x={1096.5} y={78.032} fill="#fff" rx={14.5} />
    <rect
      width={65}
      height={65}
      x={1096.5}
      y={78.032}
      stroke="#CBD5E1"
      rx={14.5}
    />
    <g filter="url(#G)">
      <path
        fill="#8E592D"
        d="M1146.6 93.466h-35.2a2.671 2.671 0 0 0-2.67 2.666v24.534a2.67 2.67 0 0 0 2.67 2.666h35.2a2.67 2.67 0 0 0 2.67-2.666V96.132c0-1.472-1.2-2.666-2.67-2.666Z"
      />
    </g>
    <g filter="url(#H)">
      <path
        fill="#000"
        fillOpacity={0.01}
        fillRule="evenodd"
        d="M1123.21 123.3s1.92-7.255 5.61-7.255c3.69 0 5.61 7.309 5.61 7.309l-11.22-.054Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#I)">
      <path
        fill="#000"
        fillOpacity={0.01}
        fillRule="evenodd"
        d="M1139.67 116.932a5.334 5.334 0 1 0-5.34-5.333 5.336 5.336 0 0 0 5.34 5.333Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#J)">
      <path
        fill="#000"
        fillOpacity={0.01}
        fillRule="evenodd"
        d="M1118.33 116.932c2.95 0 5.34-2.388 5.34-5.333a5.336 5.336 0 0 0-10.67 0 5.334 5.334 0 0 0 5.33 5.333Z"
        clipRule="evenodd"
      />
    </g>
    <g filter="url(#K)">
      <path
        fill="#D89E4E"
        fillRule="evenodd"
        d="M1149.27 96.666c1.76 0 3.2 1.43 3.2 3.201v24.531a3.205 3.205 0 0 1-3.2 3.201h-11.64c-.59 0-1.28-.429-1.54-.955l-3.81-7.806c-1.81-3.703-4.84-3.751-6.77-.101l-4.19 7.918c-.27.522-.97.944-1.56.944h-11.02c-1.77 0-3.21-1.43-3.21-3.201v-24.53a3.205 3.205 0 0 1 3.2-3.202h40.54Zm-30.94 20.267c2.95 0 5.34-2.388 5.34-5.334a5.336 5.336 0 0 0-10.67 0 5.334 5.334 0 0 0 5.33 5.334Zm21.34 0a5.334 5.334 0 1 0-.009-10.669 5.334 5.334 0 0 0 .009 10.669Z"
        clipRule="evenodd"
      />
    </g>
    <defs>
      <filter
        id="b"
        width={17.6}
        height={10.134}
        x={33.267}
        y={119.399}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feColorMatrix values="0 0 0 0 0.0784314 0 0 0 0 0.592157 0 0 0 0 0.34902 0 0 0 1 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="c"
        width={32.067}
        height={28.733}
        x={161.962}
        y={89.732}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dx={0.6} dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 0.88914 0 0 0 0 0.556562 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="d"
        width={39.609}
        height={14.866}
        x={154.495}
        y={116.466}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dx={0.6} dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.580392 0 0 0 0 0.713726 0 0 0 0 0.909804 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="j"
        width={41.067}
        height={43.066}
        x={286.467}
        y={88.999}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.639216 0 0 0 0 0.768627 0 0 0 0 0.976471 0 0 0 0.5 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.639216 0 0 0 0 0.768627 0 0 0 0 0.976471 0 0 0 0.5 0" />
        <feBlend
          in2="effect1_innerShadow_855_22350"
          result="effect2_innerShadow_855_22350"
        />
      </filter>
      <filter
        id="l"
        width={40.533}
        height={40.533}
        x={288.733}
        y={92.266}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dx={2} dy={2} />
        <feGaussianBlur stdDeviation={2} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="n"
        width={3.733}
        height={4.134}
        x={425.867}
        y={94.332}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={1} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.694265 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.8 0 0 0 0 0.807843 0 0 0 0 0.815686 0 0 0 1 0" />
        <feBlend
          in2="effect1_innerShadow_855_22350"
          result="effect2_innerShadow_855_22350"
        />
      </filter>
      <filter
        id="p"
        width={6.133}
        height={6.134}
        x={451.067}
        y={102.332}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={1} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.578182 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="r"
        width={46.8}
        height={2.533}
        x={420.6}
        y={100.665}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={0.5} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.407609 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="s"
        width={46.8}
        height={2.533}
        x={420.6}
        y={124.665}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={0.5} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.407609 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="t"
        width={20.867}
        height={21.366}
        x={433.567}
        y={102.998}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={0.25} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.579031 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="v"
        width={37.333}
        height={12.2}
        x={699.339}
        y={90.268}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.792157 0 0 0 0 0.792157 0 0 0 0 0.792157 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="x"
        width={5.2}
        height={5.2}
        x={705.772}
        y={91.867}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dx={0.5} dy={1} />
        <feGaussianBlur stdDeviation={0.5} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="z"
        width={5.2}
        height={5.2}
        x={726.039}
        y={91.867}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dx={0.5} dy={1} />
        <feGaussianBlur stdDeviation={0.5} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_855_22350"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_855_22350"
          result="shape"
        />
      </filter>
      <filter
        id="A"
        width={41.815}
        height={35.129}
        x={697.093}
        y={95.668}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.258824 0 0 0 0 0.494118 0 0 0 0 0.913725 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="B"
        width={41.815}
        height={18.066}
        x={697.093}
        y={96.668}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.282353 0 0 0 0 0.45098 0 0 0 0 0.831373 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="D"
        width={39.494}
        height={17.566}
        x={698.081}
        y={113.729}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={0.5} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.588542 0 0 0 0 0.588542 0 0 0 0 0.588542 0 0 0 0.2 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="G"
        width={40.533}
        height={31.866}
        x={1108.73}
        y={92.466}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.6 0 0 0 0 0.411765 0 0 0 0 0.258824 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.517647 0 0 0 0 0.317647 0 0 0 0 0.160784 0 0 0 1 0" />
        <feBlend
          in2="effect1_innerShadow_855_22350"
          result="effect2_innerShadow_855_22350"
        />
      </filter>
      <filter
        id="H"
        width={11.217}
        height={9.309}
        x={1123.21}
        y={116.045}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={2} />
        <feGaussianBlur stdDeviation={1} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="I"
        width={10.667}
        height={11.667}
        x={1134.33}
        y={106.266}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="J"
        width={10.667}
        height={11.667}
        x={1113}
        y={106.266}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
      </filter>
      <filter
        id="K"
        width={46.933}
        height={32.934}
        x={1105.53}
        y={95.666}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.862745 0 0 0 0 0.654902 0 0 0 0 0.372549 0 0 0 1 0" />
        <feBlend in2="shape" result="effect1_innerShadow_855_22350" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={-1} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2="hardAlpha" k2={-1} k3={1} operator="arithmetic" />
        <feColorMatrix values="0 0 0 0 0.811765 0 0 0 0 0.584314 0 0 0 0 0.270588 0 0 0 1 0" />
        <feBlend
          in2="effect1_innerShadow_855_22350"
          result="effect2_innerShadow_855_22350"
        />
      </filter>
      <linearGradient
        id="a"
        x1={32.583}
        x2={32.583}
        y1={123.769}
        y2={131.269}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#C26003" />
        <stop offset={1} stopColor="#F4B401" stopOpacity={0.514} />
      </linearGradient>
      <linearGradient
        id="e"
        x1={149.904}
        x2={149.904}
        y1={111.339}
        y2={132.065}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#155ACA" />
        <stop offset={1} stopColor="#2F71DD" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="f"
        x1={154.302}
        x2={158.994}
        y1={125.687}
        y2={128.771}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#002D77" stopOpacity={0.802} />
        <stop offset={1} stopColor="#2F71DD" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="g"
        x1={161.755}
        x2={171.299}
        y1={100.445}
        y2={105.704}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#1B935A" />
        <stop offset={1} stopColor="#118B50" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="i"
        x1={297.4}
        x2={320.23}
        y1={123.797}
        y2={146.924}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3C70C8" />
        <stop offset={1} stopColor="#4889F4" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="m"
        x1={425.136}
        x2={425.136}
        y1={89.463}
        y2={120.453}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#494949" />
        <stop offset={1} stopColor="#2B2B2B" />
      </linearGradient>
      <linearGradient
        id="o"
        x1={425.867}
        x2={425.867}
        y1={95.332}
        y2={97.465}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#494949" />
        <stop offset={1} stopColor="#2B2B2B" />
      </linearGradient>
      <linearGradient
        id="q"
        x1={452.584}
        x2={452.584}
        y1={102.367}
        y2={105.465}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#E9CD78" />
        <stop offset={1} stopColor="#E7B61D" />
      </linearGradient>
      <linearGradient
        id="u"
        x1={578.137}
        x2={582.137}
        y1={122.731}
        y2={98.731}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FB923C" />
        <stop offset={0.496} stopColor="#EF4444" />
        <stop offset={1} stopColor="#701A75" />
      </linearGradient>
      <linearGradient
        id="w"
        x1={704.893}
        x2={708.054}
        y1={95.379}
        y2={101.257}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#B0B0B0" />
        <stop offset={1} stopColor="#D8D8D8" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="y"
        x1={725.16}
        x2={728.321}
        y1={95.379}
        y2={101.257}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#B0B0B0" />
        <stop offset={1} stopColor="#D8D8D8" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="C"
        x1={704.649}
        x2={706.44}
        y1={115.938}
        y2={131.865}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3C78DD" />
        <stop offset={1} stopColor="#4688F4" stopOpacity={0.01} />
      </linearGradient>
      <linearGradient
        id="E"
        x1={828.333}
        x2={828.333}
        y1={83.865}
        y2={137.199}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#E8016A" />
        <stop offset={1} stopColor="#EE3224" />
      </linearGradient>
      <linearGradient
        id="F"
        x1={963.944}
        x2={963.944}
        y1={89.813}
        y2={131.252}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#19DEFE" />
        <stop offset={1} stopColor="#1E5BEF" />
      </linearGradient>
    </defs>
  </svg>
);
export default Logos;
