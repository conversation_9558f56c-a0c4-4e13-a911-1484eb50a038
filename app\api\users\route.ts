import prisma from "@/lib/db";
import {
  PrismaClientKnownRequestError,
} from "@prisma/client/runtime/library";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (req: NextRequest, res: NextResponse) => {
  const data = await req.json();
  try {
    const user = await prisma.user.create({ data });
    return NextResponse.json({message:"User Created Successfully 😁 ",user});
  } catch (e) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === "P2002") {
        return NextResponse.json({message:"Email already exists"}, { status: 409 });
      }
      console.log(e);
      return NextResponse.json({message:"Errors Just Happen Bro 😁 "}, { status: 500 });
    } else {
      return NextResponse.json({message:"Errors Just Happen Bro 😁 "}, { status: 409 });
    }

  }
};
