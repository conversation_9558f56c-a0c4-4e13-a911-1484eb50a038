"use client";
import {
  Ch<PERSON>ronDown,
  Menu,
  Search,
  ShoppingBasket,
  User,
  Facebook,
  Instagram,
  Twitter,
  Mail,
  Globe,
  DollarSign,
} from "lucide-react";
import Logo from "@/components/Logo";
import Link from "next/link";

import Header from "@/components/header";
import { ChatbotWidget } from "@/components/chatbot";
import { motion } from "framer-motion";
import Footer from "@/components/footer";
import { AuthProvider } from "@/lib/auth-context";
import { Toaster } from "@/components/ui/toaster";

// Animation variants for page layout elements
const headerVariants = {
  hidden: { opacity: 0, y: -10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

const footerVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
      delay: 0.3,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthProvider>
      <motion.div initial="hidden" animate="visible" variants={headerVariants}>
        <Header />
      </motion.div>

      <div className="page-content-wrapper">{children}</div>

      <ChatbotWidget />

      <motion.div initial="hidden" animate="visible" variants={footerVariants}>
        <Footer />
      </motion.div>

      <Toaster />
    </AuthProvider>
  );
}
