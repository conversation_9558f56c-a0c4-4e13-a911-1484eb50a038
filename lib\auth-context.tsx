"use client";

import React, { createContext, useState, useContext, useEffect } from "react";

interface User {
    id: string;
    name: string;
    email: string;
    role: string;
}

interface AuthContextType {
    user: User | null;
    isLoading: boolean;
    signIn: (email: string, password: string) => Promise<boolean>;
    signUp: (
        email: string,
        password: string,
        firstName: string,
        lastName: string
    ) => Promise<boolean>;
    signOut: () => void;
    forgotPassword: (email: string) => Promise<boolean>;
    resetPassword: (token: string, password: string) => Promise<boolean>;
    signInWithGoogle: () => Promise<boolean>;
    signInWithFacebook: () => Promise<boolean>;
    signInWithApple: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType>({
    user: null,
    isLoading: true,
    signIn: async () => false,
    signUp: async () => false,
    signOut: () => { },
    forgotPassword: async () => false,
    resetPassword: async () => false,
    signInWithGoogle: async () => false,
    signInWithFacebook: async () => false,
    signInWithApple: async () => false,
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    // Check for existing session
    useEffect(() => {
        const checkAuth = async () => {
            try {
                // Here you would call your API to validate session
                // For demo purposes we'll just check localStorage
                const storedUser = localStorage.getItem("user");
                if (storedUser) {
                    setUser(JSON.parse(storedUser));
                }
            } catch (error) {
                console.error("Auth check failed:", error);
            } finally {
                setIsLoading(false);
            }
        };

        checkAuth();
    }, []);

    // Sign in function
    const signIn = async (email: string, password: string): Promise<boolean> => {
        setIsLoading(true);
        try {
            // In a real app, you would call your API here
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Mock successful authentication
            if (email && password) {
                const mockUser = {
                    id: "user-123",
                    name: "Demo User",
                    email: email,
                    role: "user",
                };

                setUser(mockUser);
                localStorage.setItem("user", JSON.stringify(mockUser));
                return true;
            }
            return false;
        } catch (error) {
            console.error("Sign in failed:", error);
            return false;
        } finally {
            setIsLoading(false);
        }
    };

    // Sign up function
    const signUp = async (
        email: string,
        password: string,
        firstName: string,
        lastName: string
    ): Promise<boolean> => {
        setIsLoading(true);
        try {
            // In a real app, you would call your API here
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Mock successful registration
            if (email && password && firstName && lastName) {
                const mockUser = {
                    id: "user-" + Math.floor(Math.random() * 1000),
                    name: `${firstName} ${lastName}`,
                    email: email,
                    role: "user",
                };

                setUser(mockUser);
                localStorage.setItem("user", JSON.stringify(mockUser));
                return true;
            }
            return false;
        } catch (error) {
            console.error("Sign up failed:", error);
            return false;
        } finally {
            setIsLoading(false);
        }
    };

    // Sign out function
    const signOut = () => {
        localStorage.removeItem("user");
        setUser(null);
    };

    // Forgot password function
    const forgotPassword = async (email: string): Promise<boolean> => {
        try {
            // In a real app, you would call your API here
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return true;
        } catch (error) {
            console.error("Forgot password request failed:", error);
            return false;
        }
    };

    // Reset password function
    const resetPassword = async (
        token: string,
        password: string
    ): Promise<boolean> => {
        try {
            // In a real app, you would call your API here
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return true;
        } catch (error) {
            console.error("Reset password failed:", error);
            return false;
        }
    };
    // Social sign-in methods
    const signInWithGoogle = async (): Promise<boolean> => {
        try {
            // In a real app, you would call your API to authenticate with Google
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Mock successful authentication
            const mockUser = {
                id: "google-user-123",
                name: "Google User",
                email: "<EMAIL>",
                role: "user",
            };

            setUser(mockUser);
            localStorage.setItem("user", JSON.stringify(mockUser));
            return true;
        } catch (error) {
            console.error("Google sign in failed:", error);
            return false;
        }
    };

    const signInWithFacebook = async (): Promise<boolean> => {
        try {
            // In a real app, you would call your API to authenticate with Facebook
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Mock successful authentication
            const mockUser = {
                id: "facebook-user-123",
                name: "Facebook User",
                email: "<EMAIL>",
                role: "user",
            };

            setUser(mockUser);
            localStorage.setItem("user", JSON.stringify(mockUser));
            return true;
        } catch (error) {
            console.error("Facebook sign in failed:", error);
            return false;
        }
    };

    const signInWithApple = async (): Promise<boolean> => {
        try {
            // In a real app, you would call your API to authenticate with Apple
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Mock successful authentication
            const mockUser = {
                id: "apple-user-123",
                name: "Apple User",
                email: "<EMAIL>",
                role: "user",
            };

            setUser(mockUser);
            localStorage.setItem("user", JSON.stringify(mockUser));
            return true;
        } catch (error) {
            console.error("Apple sign in failed:", error);
            return false;
        }
    };

    const value = {
        user,
        isLoading,
        signIn,
        signUp,
        signOut,
        forgotPassword,
        resetPassword,
        signInWithGoogle,
        signInWithFacebook,
        signInWithApple,
    };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => useContext(AuthContext);