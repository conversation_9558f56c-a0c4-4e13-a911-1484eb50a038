"use client";

import Image from "next/image";
import { PropsWithChildren } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import Logo from "@/components/Logo";

interface AuthFormLayoutProps {
  imageUrl: string;
  testimonialText: string;
  testimonialAuthor: string;
  testimonialRole: string;
  avatarSrc?: string;
}

export function AuthFormLayout({
  children,
  imageUrl,
  testimonialText,
  testimonialAuthor,
  testimonialRole,
  avatarSrc = "/ava.jpeg",
}: PropsWithChildren<AuthFormLayoutProps>) {
  return (
    <div className="min-h-screen w-full">
      <div className="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
        {/* Form Section - Scrollable */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-col justify-center min-h-screen overflow-y-auto px-8 py-10 md:p-12 lg:p-8 bg-white relative"
        >
          {/* Background decorative elements */}
          <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
            <div className="absolute top-0 right-0 w-96 h-96 bg-primary rounded-full -translate-y-1/2 translate-x-1/2 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary rounded-full translate-y-1/2 -translate-x-1/2 blur-3xl"></div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mx-auto w-full max-w-md relative z-10 flex flex-col py-8"
          >
            <div className="flex-shrink-0 mb-6 text-center">
              <Link href="/" className="inline-block mb-6">
                <Logo />
              </Link>
            </div>

            <div className="flex-1">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="space-y-5"
              >
                {children}
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {/* Image Section - Fixed/Sticky */}
        <div className="hidden lg:block fixed right-0 top-0 w-1/2 h-screen overflow-hidden">
          <motion.div
            initial={{ scale: 1.1 }}
            animate={{ scale: 1 }}
            transition={{ duration: 1.5 }}
            className="h-full w-full"
          >
            <Image
              src={imageUrl}
              alt="Authentication background"
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
          </motion.div>

          {/* Brand overlay in top right */}
          <div className="absolute top-8 right-8">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="p-3 bg-white/10 backdrop-blur-md rounded-xl"
            >
              <Logo />
            </motion.div>
          </div>

          {/* Decorative elements */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.6 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="absolute top-1/4 left-1/4 w-72 h-72 rounded-full bg-primary/30 filter blur-3xl"
          />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="absolute bottom-1/3 right-1/4 w-64 h-64 rounded-full bg-secondary/30 filter blur-3xl"
          />

          {/* Additional decorative elements */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.4 }}
            transition={{ delay: 0.7, duration: 0.8 }}
            className="absolute top-2/3 right-1/3 w-40 h-40 rounded-full bg-blue-500/30 filter blur-2xl"
          />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.3 }}
            transition={{ delay: 0.9, duration: 0.8 }}
            className="absolute top-1/3 right-1/4 w-32 h-32 rounded-full bg-purple-500/30 filter blur-xl"
          />

          {/* Testimonials */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="absolute bottom-12 left-8 right-8 rounded-2xl bg-white/15 p-8 backdrop-blur-md border border-white/20 shadow-xl"
          >
            <div className="relative">
              {/* Quote marks */}
              <div className="absolute -top-6 -left-2 text-white/20 text-6xl font-serif">
                "
              </div>
              <div className="absolute -bottom-10 -right-2 text-white/20 text-6xl font-serif">
                "
              </div>

              <p className="text-white/90 text-lg italic mb-6 relative z-10">
                "{testimonialText}"
              </p>

              <div className="flex items-center gap-4 mt-4">
                <div className="relative flex-shrink-0 h-14 w-14 overflow-hidden rounded-full bg-primary/20 ring-2 ring-white/30 shadow-xl">
                  <Image
                    src={avatarSrc}
                    alt="User avatar"
                    width={56}
                    height={56}
                    className="h-full w-full object-cover flex-shrink-0"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-white">
                    {testimonialAuthor}
                  </p>
                  <p className="text-xs text-white/70">{testimonialRole}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
