"use client";
import InView from "@/components/InView";
import Logo from "@/components/Logo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { product } from "@/data/product";
import { <PERSON>R<PERSON>, Heart, ShoppingBag, Star } from "lucide-react";
import Link from "next/link";
import React from "react";
import { motion } from "framer-motion";

function Page() {
  return (
    <div className="">
      <div className="relative overflow-hidden bg-gradient-to-b from-white to-gray-50 min-h-[calc(100vh-72px)]">
        {/* Background pattern elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -right-40 -top-40 h-[500px] w-[500px] rounded-full border border-gray-200 opacity-20"></div>
          <div className="absolute -bottom-40 -left-40 h-[600px] w-[600px] rounded-full border border-gray-200 opacity-20"></div>
          <div className="absolute right-1/4 top-1/3 h-[300px] w-[300px] rounded-full border border-gray-200 opacity-20"></div>
        </div>

        <main className="container relative mx-auto px-4 py-12 flex flex-col md:flex-row items-center min-h-[calc(100vh-72px)]">
          <div className="w-full md:w-1/2 md:pr-12 mb-12 md:mb-0 z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="space-y-6"
            >
              <div className="inline-block">
                <span className="inline-flex items-center gap-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-black text-white">
                  New Collection{" "}
                  <span className="relative flex h-2 w-2">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-white"></span>
                  </span>
                </span>
              </div>

              <h1 className="text-5xl md:text-7xl font-bold tracking-tight">
                <span className="block">AirNags®</span>
                <span className="block text-black/75">Premium Quality</span>
              </h1>

              <p className="text-lg md:text-xl text-gray-600 max-w-md">
                Keep your everyday style chic and on-trend with our carefully
                curated collection of 20+ premium styles designed for your
                comfort.
              </p>

              <div className="flex flex-wrap gap-4 pt-4">
                <Button className="rounded-full h-14 px-8 bg-black hover:bg-black/90 text-white shadow-lg hover:shadow-xl transition-all">
                  Shop Collection
                </Button>
                <Button
                  variant="outline"
                  className="rounded-full h-14 px-8 border-gray-300 hover:border-black hover:bg-black/5 transition-all flex items-center gap-2"
                >
                  Explore More
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-8 pt-6">
                <div className="flex -space-x-3">
                  {[1, 2, 3, 4].map((item) => (
                    <div
                      key={item}
                      className="h-10 w-10 rounded-full border-2 border-white overflow-hidden"
                    >
                      <img
                        src={`https://randomuser.me/api/portraits/men/${item + 10}.jpg`}
                        alt="Customer"
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ))}
                  <div className="h-10 w-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs font-medium">
                    +2k
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((item) => (
                      <Star
                        key={item}
                        className="h-4 w-4 fill-amber-400 text-amber-400"
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    From 10k+ happy customers
                  </span>
                </div>
              </div>
            </motion.div>
          </div>

          <div className="w-full md:w-1/2 flex justify-center z-10">
            <motion.div
              initial={{ opacity: 0, scale: 0.9, rotate: -5 }}
              animate={{ opacity: 1, scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
              className="relative"
            >
              <div className="absolute -inset-0.5 bg-gradient-to-r from-pink-600 to-purple-600 opacity-20 blur-2xl rounded-full"></div>
              <img
                src="/955.png"
                alt="AirNags Shoe"
                className="relative w-full max-w-lg h-auto object-contain transform hover:rotate-2 transition-transform duration-700"
              />

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
                className="absolute -right-6 bottom-10 bg-white rounded-xl shadow-xl p-3 max-w-[180px]"
              >
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <img
                      src="/651.webp"
                      alt="Small shoe"
                      className="h-6 w-6 object-contain"
                    />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Limited Stock</h4>
                    <div className="h-1.5 w-24 bg-gray-100 rounded-full overflow-hidden">
                      <div className="h-full w-1/5 bg-red-500 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.5 }}
                className="absolute -left-6 top-10 bg-white rounded-xl shadow-xl p-3 max-w-[160px]"
              >
                <div className="text-center">
                  <span className="text-xs text-gray-500">Premium Design</span>
                  <h4 className="text-sm font-medium">Summer Collection</h4>
                  <div className="flex justify-center mt-1 gap-1">
                    <span className="h-2 w-2 rounded-full bg-black"></span>
                    <span className="h-2 w-2 rounded-full bg-gray-300"></span>
                    <span className="h-2 w-2 rounded-full bg-gray-300"></span>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
      <div className="mx-auto container py-16 px-4 relative">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(100,100,250,0.05),transparent_70%)]"></div>

        <div className="relative z-10 mb-14">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
            <div className="max-w-xl">
              <div className="inline-flex items-center gap-2 mb-2 bg-purple-50 px-3 py-1 rounded-full">
                <span className="h-2 w-2 rounded-full bg-purple-600 animate-pulse"></span>
                <Badge variant="outline" className="text-purple-600 border-none bg-transparent">
                  Just Dropped
                </Badge>
              </div>
              <h3 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-purple-900 to-gray-700">
                Latest Arrivals
              </h3>
              <p className="text-gray-500 mt-4 max-w-md">
                Be the first to experience our newest designs, featuring cutting-edge innovation and premium craftsmanship.
              </p>
            </div>
            <Button variant="outline" className="group mt-6 md:mt-0 h-12 px-6 rounded-full gap-2 border-gray-300 border-2 hover:border-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md">
              View All Products
              <span className="relative w-5 h-5 overflow-hidden inline-flex items-center justify-center">
                <ArrowRight className="h-4 w-4 absolute group-hover:translate-x-8 group-hover:opacity-0 transition-all duration-300" />
                <ArrowRight className="h-4 w-4 absolute -translate-x-8 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-300" />
              </span>
            </Button>
          </div>
        </div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-50px" }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {product.map((item, idx) => (
            <motion.div
              key={item.name}
              variants={{
                hidden: { opacity: 0, y: 30 },
                visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } }
              }}
              whileHover={{ y: -8, transition: { duration: 0.2 } }}
              className="group relative bg-white rounded-2xl overflow-hidden transition-all duration-300 shadow-sm hover:shadow-xl border border-gray-100"
            >
              <Link href={`/products/${item.name.replaceAll(" ", "-").toLowerCase()}`}>
                <div className="relative h-80 overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover object-center transition-all duration-700 group-hover:scale-110"
                  />

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Product badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    <span className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg">
                      NEW
                    </span>
                    {idx % 2 === 0 && (
                      <span className="bg-red-500 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg flex items-center gap-1">
                        <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 4L13.4131 7.73459L17.4271 7.2459L14.7531 10.0164L16.5725 13.5836L12.7653 12.7654L10.0164 15.4729L9.49591 11.4729L5.76142 10.06L9.33713 8.2275L8.53296 4.41913L11.9835 7.07538L15.0805 4.4131L14.5795 8.42705L18.3271 9.84L12 4Z" fill="currentColor" />
                        </svg>
                        <span>-20%</span>
                      </span>
                    )}
                  </div>

                  {/* Action buttons */}
                  <div className="absolute right-4 top-4 flex flex-col gap-2 translate-x-12 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-300">
                    <button className="bg-white/90 backdrop-blur-sm rounded-full p-2.5 shadow-md hover:bg-white hover:shadow-lg transition-all">
                      <Heart className="h-4 w-4 text-gray-700" />
                    </button>
                    <button className="bg-white/90 backdrop-blur-sm rounded-full p-2.5 shadow-md hover:bg-white hover:shadow-lg transition-all">
                      <ShoppingBag className="h-4 w-4 text-gray-700" />
                    </button>
                  </div>

                  {/* Quick view button */}
                  <div className="absolute inset-x-0 bottom-0 p-4 opacity-0 translate-y-4 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-300">
                    <Button size="sm" variant="secondary" className="w-full rounded-full bg-white/90 backdrop-blur-sm text-gray-800 hover:bg-black hover:text-white transition-all">
                      Quick View
                    </Button>
                  </div>
                </div>

                <div className="p-5">
                  {/* Rating display */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < 4 ? "text-amber-400 fill-amber-400" : "text-gray-300 fill-gray-300"}`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 font-medium">
                      {Math.floor(Math.random() * 200) + 50} reviews
                    </span>
                  </div>

                  {/* Product category */}
                  <div className="mb-1">
                    <span className="text-xs font-medium text-purple-600 uppercase tracking-wider">
                      {["Running", "Casual", "Sport", "Training"][idx % 4]}
                    </span>
                  </div>

                  {/* Product name */}
                  <h3 className="text-lg font-bold text-gray-800 mb-1 group-hover:text-purple-600 transition-colors">
                    {item.name}
                  </h3>

                  {/* Product description */}
                  <p className="text-sm text-gray-500 mb-4 line-clamp-2">
                    Premium quality footwear with enhanced comfort and durability
                  </p>

                  {/* Price and availability */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-gray-900">
                        {item.price} MAD
                      </span>
                      <span className="text-sm text-gray-400 line-through">
                        {Math.round(parseInt(item.price) * 1.2)} MAD
                      </span>
                    </div>
                    <div className="bg-green-50 text-green-600 text-xs font-medium px-2.5 py-1 rounded-full">
                      In Stock
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <div className="flex items-center justify-center mt-16">
          <Button variant="default" className="rounded-full group h-12 px-8 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 shadow-md hover:shadow-lg transition-all duration-300">
            <span>Load More Products</span>
            <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
      <div className="container mx-auto px-4 py-6">
        <div className="bg-gradient-to-r from-gray-100 to-pink-100 p-8 md:p-12 flex flex-col md:flex-row items-center justify-between">
          <div className="mb-6 md:mb-0 md:mr-8 max-w-md">
            <h2 className="text-3xl md:text-4xl font-bold mb-2">
              Join our newsletter.
            </h2>
            <p className="text-xl md:text-2xl mb-6">Enjoy big discounts.</p>
            <form className="space-y-4">
              <div className="flex flex-col sm:flex-row">
                <input
                  type="email"
                  placeholder="Your email"
                  className="flex-grow px-4 py-2 rounded-l-md border-2 border-gray-300 focus:outline-none focus:border-blue-500"
                />
                <button
                  type="submit"
                  className="mt-2 sm:mt-0 px-6 py-2 bg-black text-white font-semibold rounded-r-md hover:bg-gray-800 transition duration-300"
                >
                  Signup
                </button>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="marketing-consent"
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label
                  htmlFor="marketing-consent"
                  className="ml-2 text-sm text-gray-700"
                >
                  I agree to receive marketing emails.
                </label>
              </div>
            </form>
          </div>
          <div className="w-full md:w-1/2 lg:w-1/3 h-72">
            <img
              src="/955.png"
              alt="Black Nike sneaker"
              className="w-full h-full object-contain transform -rotate-12 hover:rotate-0 transition-transform duration-300"
            />
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {/* Free Shipping */}
          <div className="flex items-center border-b border-gray-200 p-4 md:border-b-0 md:border-r">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mr-3 h-6 w-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"
              />
            </svg>
            <div>
              <h3 className="text-lg font-semibold">Free Shipping</h3>
              <p className="text-sm text-gray-500">Orders above $200</p>
            </div>
          </div>
          {/* Money-back */}
          <div className="flex items-center border-b border-gray-200 p-4 md:border-b-0 lg:border-r">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mr-3 h-6 w-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
              />
            </svg>
            <div>
              <h3 className="text-lg font-semibold">Money-back</h3>
              <p className="text-sm text-gray-500">30 day Guarantee</p>
            </div>
          </div>
          {/* Premium Support */}
          <div className="flex items-center border-b border-gray-200 p-4 lg:border-b-0 lg:border-r">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mr-3 h-6 w-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
            <div>
              <h3 className="text-lg font-semibold">Premium Support</h3>
              <p className="text-sm text-gray-500">Phone and email support</p>
            </div>
          </div>
          {/* Secure Payments */}
          <div className="flex items-center p-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mr-3 h-6 w-6 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <div>
              <h3 className="text-lg font-semibold">Secure Payments</h3>
              <p className="text-sm text-gray-500">Secured by Stripe</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}



export default Page;
