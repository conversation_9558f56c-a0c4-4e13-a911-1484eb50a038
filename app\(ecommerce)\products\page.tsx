"use client";

import { useState, useMemo } from "react";
import { motion } from "framer-motion";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Star,
  Heart,
  ShoppingBag,
  SlidersHorizontal,
  ArrowUpDown,
  X,
  ChevronRight,
  Eye,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { product, Product } from "@/data/product";
import { categories } from "@/data/categories";

export default function ProductsPage() {
  // State for filters and sorting
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("featured");
  const [priceRange, setPriceRange] = useState([0, 500]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [selectedSizes, setSelectedSizes] = useState<string[]>([]);
  const [selectedRatings, setSelectedRatings] = useState<number[]>([]);
  const [showOnSale, setShowOnSale] = useState(false);
  const [showInStock, setShowInStock] = useState(false);
  const [showNewArrivals, setShowNewArrivals] = useState(false);

  // Get unique values for filters
  const uniqueBrands = [
    ...new Set(product.map((p) => p.brand).filter(Boolean)),
  ];
  const uniqueColors = [...new Set(product.flatMap((p) => p.colors || []))];
  const uniqueSizes = [...new Set(product.flatMap((p) => p.sizes || []))];

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = product.filter((prod) => {
      // Search filter
      if (
        searchQuery &&
        !prod.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !prod.description.toLowerCase().includes(searchQuery.toLowerCase())
      ) {
        return false;
      }

      // Price filter
      if (prod.price < priceRange[0] || prod.price > priceRange[1])
        return false;

      // Category filter
      if (
        selectedCategories.length > 0 &&
        !selectedCategories.includes(prod.categorySlug)
      )
        return false;

      // Brand filter
      if (
        selectedBrands.length > 0 &&
        !selectedBrands.includes(prod.brand || "")
      )
        return false;

      // Color filter
      if (
        selectedColors.length > 0 &&
        !prod.colors?.some((color) => selectedColors.includes(color))
      )
        return false;

      // Size filter
      if (
        selectedSizes.length > 0 &&
        !prod.sizes?.some((size) => selectedSizes.includes(size))
      )
        return false;

      // Rating filter
      if (
        selectedRatings.length > 0 &&
        !selectedRatings.some((rating) => prod.rating >= rating)
      )
        return false;

      // Sale filter
      if (showOnSale && !prod.isOnSale) return false;

      // Stock filter
      if (showInStock && !prod.inStock) return false;

      // New arrivals filter
      if (showNewArrivals && !prod.isNew) return false;

      return true;
    });

    // Sort products
    switch (sortBy) {
      case "price-low":
        filtered.sort((a, b) => a.price - b.price);
        break;
      case "price-high":
        filtered.sort((a, b) => b.price - a.price);
        break;
      case "rating":
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case "newest":
        filtered.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0));
        break;
      case "name":
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      default:
        // Featured - keep original order
        break;
    }

    return filtered;
  }, [
    product,
    searchQuery,
    priceRange,
    selectedCategories,
    selectedBrands,
    selectedColors,
    selectedSizes,
    selectedRatings,
    showOnSale,
    showInStock,
    showNewArrivals,
    sortBy,
  ]);

  const clearFilters = () => {
    setSearchQuery("");
    setPriceRange([0, 500]);
    setSelectedCategories([]);
    setSelectedBrands([]);
    setSelectedColors([]);
    setSelectedSizes([]);
    setSelectedRatings([]);
    setShowOnSale(false);
    setShowInStock(false);
    setShowNewArrivals(false);
  };

  const hasActiveFilters =
    searchQuery ||
    priceRange[0] > 0 ||
    priceRange[1] < 500 ||
    selectedCategories.length > 0 ||
    selectedBrands.length > 0 ||
    selectedColors.length > 0 ||
    selectedSizes.length > 0 ||
    selectedRatings.length > 0 ||
    showOnSale ||
    showInStock ||
    showNewArrivals;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumbs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-3 max-w-7xl">
          <div className="flex items-center text-sm text-gray-500">
            <Link href="/" className="hover:text-black transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-gray-900 font-medium">Products</span>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-700 text-white py-16">
        <div className="container mx-auto px-4 max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Discover Our Products
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Explore our curated collection of premium products designed for
              your lifestyle
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-gray-300 focus:bg-white/20"
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters - Desktop */}
          <div className="hidden lg:block w-80 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm border p-6 sticky top-24">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <SlidersHorizontal className="h-5 w-5" />
                  Filters
                </h3>
                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    Clear All
                  </Button>
                )}
              </div>

              <div className="space-y-6">
                {/* Price Range */}
                <div>
                  <h4 className="font-medium mb-3">Price Range</h4>
                  <Slider
                    value={priceRange}
                    onValueChange={setPriceRange}
                    max={500}
                    step={10}
                    className="mb-2"
                  />
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>${priceRange[0]}</span>
                    <span>${priceRange[1]}</span>
                  </div>
                </div>

                {/* Categories */}
                <div>
                  <h4 className="font-medium mb-3">Categories</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {categories.map((category) => (
                      <div
                        key={category.id}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`category-${category.id}`}
                          checked={selectedCategories.includes(category.slug)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedCategories([
                                ...selectedCategories,
                                category.slug,
                              ]);
                            } else {
                              setSelectedCategories(
                                selectedCategories.filter(
                                  (c) => c !== category.slug
                                )
                              );
                            }
                          }}
                        />
                        <label
                          htmlFor={`category-${category.id}`}
                          className="text-sm cursor-pointer flex-1"
                        >
                          {category.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Brands */}
                <div>
                  <h4 className="font-medium mb-3">Brands</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {uniqueBrands.map((brand) => (
                      <div key={brand} className="flex items-center space-x-2">
                        <Checkbox
                          id={`brand-${brand}`}
                          checked={selectedBrands.includes(brand)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedBrands([...selectedBrands, brand]);
                            } else {
                              setSelectedBrands(
                                selectedBrands.filter((b) => b !== brand)
                              );
                            }
                          }}
                        />
                        <label
                          htmlFor={`brand-${brand}`}
                          className="text-sm cursor-pointer flex-1"
                        >
                          {brand}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Colors */}
                <div>
                  <h4 className="font-medium mb-3">Colors</h4>
                  <div className="grid grid-cols-4 gap-2">
                    {uniqueColors.map((color) => (
                      <div
                        key={color}
                        className={`w-8 h-8 rounded-full border-2 cursor-pointer transition-all ${
                          selectedColors.includes(color)
                            ? "border-black scale-110"
                            : "border-gray-300 hover:border-gray-400"
                        }`}
                        style={{ backgroundColor: color.toLowerCase() }}
                        onClick={() => {
                          if (selectedColors.includes(color)) {
                            setSelectedColors(
                              selectedColors.filter((c) => c !== color)
                            );
                          } else {
                            setSelectedColors([...selectedColors, color]);
                          }
                        }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>

                {/* Sizes */}
                <div>
                  <h4 className="font-medium mb-3">Sizes</h4>
                  <div className="grid grid-cols-4 gap-2">
                    {uniqueSizes.map((size) => (
                      <Button
                        key={size}
                        variant={
                          selectedSizes.includes(size) ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          if (selectedSizes.includes(size)) {
                            setSelectedSizes(
                              selectedSizes.filter((s) => s !== size)
                            );
                          } else {
                            setSelectedSizes([...selectedSizes, size]);
                          }
                        }}
                        className="text-xs"
                      >
                        {size}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Rating */}
                <div>
                  <h4 className="font-medium mb-3">Minimum Rating</h4>
                  <div className="space-y-2">
                    {[4, 3, 2, 1].map((rating) => (
                      <div key={rating} className="flex items-center space-x-2">
                        <Checkbox
                          id={`rating-${rating}`}
                          checked={selectedRatings.includes(rating)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedRatings([...selectedRatings, rating]);
                            } else {
                              setSelectedRatings(
                                selectedRatings.filter((r) => r !== rating)
                              );
                            }
                          }}
                        />
                        <label
                          htmlFor={`rating-${rating}`}
                          className="text-sm cursor-pointer flex items-center gap-1"
                        >
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3 w-3 ${
                                  i < rating
                                    ? "fill-yellow-400 text-yellow-400"
                                    : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          & up
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Special Filters */}
                <div>
                  <h4 className="font-medium mb-3">Special Offers</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="on-sale"
                        checked={showOnSale}
                        onCheckedChange={setShowOnSale}
                      />
                      <label
                        htmlFor="on-sale"
                        className="text-sm cursor-pointer"
                      >
                        On Sale
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="in-stock"
                        checked={showInStock}
                        onCheckedChange={setShowInStock}
                      />
                      <label
                        htmlFor="in-stock"
                        className="text-sm cursor-pointer"
                      >
                        In Stock Only
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="new-arrivals"
                        checked={showNewArrivals}
                        onCheckedChange={setShowNewArrivals}
                      />
                      <label
                        htmlFor="new-arrivals"
                        className="text-sm cursor-pointer"
                      >
                        New Arrivals
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="bg-white rounded-xl shadow-sm border p-4 mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">
                    {filteredProducts.length} products found
                  </span>
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="text-xs">
                      Filtered
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {/* Mobile Filter Button */}
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="outline" size="sm" className="lg:hidden">
                        <Filter className="h-4 w-4 mr-2" />
                        Filters
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="w-80">
                      <SheetHeader>
                        <SheetTitle>Filters</SheetTitle>
                        <SheetDescription>
                          Refine your search to find the perfect products
                        </SheetDescription>
                      </SheetHeader>

                      <div className="mt-6 space-y-6 overflow-y-auto max-h-[calc(100vh-200px)]">
                        {/* Clear All Button */}
                        {hasActiveFilters && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={clearFilters}
                            className="w-full"
                          >
                            Clear All Filters
                          </Button>
                        )}

                        {/* Price Range */}
                        <div>
                          <h4 className="font-medium mb-3">Price Range</h4>
                          <Slider
                            value={priceRange}
                            onValueChange={setPriceRange}
                            max={500}
                            step={10}
                            className="mb-2"
                          />
                          <div className="flex justify-between text-sm text-gray-600">
                            <span>${priceRange[0]}</span>
                            <span>${priceRange[1]}</span>
                          </div>
                        </div>

                        {/* Categories */}
                        <div>
                          <h4 className="font-medium mb-3">Categories</h4>
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {categories.map((category) => (
                              <div
                                key={category.id}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={`mobile-category-${category.id}`}
                                  checked={selectedCategories.includes(
                                    category.slug
                                  )}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedCategories([
                                        ...selectedCategories,
                                        category.slug,
                                      ]);
                                    } else {
                                      setSelectedCategories(
                                        selectedCategories.filter(
                                          (c) => c !== category.slug
                                        )
                                      );
                                    }
                                  }}
                                />
                                <label
                                  htmlFor={`mobile-category-${category.id}`}
                                  className="text-sm cursor-pointer flex-1"
                                >
                                  {category.name}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Brands */}
                        <div>
                          <h4 className="font-medium mb-3">Brands</h4>
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {uniqueBrands.map((brand) => (
                              <div
                                key={brand}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={`mobile-brand-${brand}`}
                                  checked={selectedBrands.includes(brand)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedBrands([
                                        ...selectedBrands,
                                        brand,
                                      ]);
                                    } else {
                                      setSelectedBrands(
                                        selectedBrands.filter(
                                          (b) => b !== brand
                                        )
                                      );
                                    }
                                  }}
                                />
                                <label
                                  htmlFor={`mobile-brand-${brand}`}
                                  className="text-sm cursor-pointer flex-1"
                                >
                                  {brand}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Colors */}
                        <div>
                          <h4 className="font-medium mb-3">Colors</h4>
                          <div className="grid grid-cols-4 gap-2">
                            {uniqueColors.map((color) => (
                              <div
                                key={color}
                                className={`w-8 h-8 rounded-full border-2 cursor-pointer transition-all ${
                                  selectedColors.includes(color)
                                    ? "border-black scale-110"
                                    : "border-gray-300 hover:border-gray-400"
                                }`}
                                style={{ backgroundColor: color.toLowerCase() }}
                                onClick={() => {
                                  if (selectedColors.includes(color)) {
                                    setSelectedColors(
                                      selectedColors.filter((c) => c !== color)
                                    );
                                  } else {
                                    setSelectedColors([
                                      ...selectedColors,
                                      color,
                                    ]);
                                  }
                                }}
                                title={color}
                              />
                            ))}
                          </div>
                        </div>

                        {/* Sizes */}
                        <div>
                          <h4 className="font-medium mb-3">Sizes</h4>
                          <div className="grid grid-cols-4 gap-2">
                            {uniqueSizes.map((size) => (
                              <Button
                                key={size}
                                variant={
                                  selectedSizes.includes(size)
                                    ? "default"
                                    : "outline"
                                }
                                size="sm"
                                onClick={() => {
                                  if (selectedSizes.includes(size)) {
                                    setSelectedSizes(
                                      selectedSizes.filter((s) => s !== size)
                                    );
                                  } else {
                                    setSelectedSizes([...selectedSizes, size]);
                                  }
                                }}
                                className="text-xs"
                              >
                                {size}
                              </Button>
                            ))}
                          </div>
                        </div>

                        {/* Rating */}
                        <div>
                          <h4 className="font-medium mb-3">Minimum Rating</h4>
                          <div className="space-y-2">
                            {[4, 3, 2, 1].map((rating) => (
                              <div
                                key={rating}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={`mobile-rating-${rating}`}
                                  checked={selectedRatings.includes(rating)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedRatings([
                                        ...selectedRatings,
                                        rating,
                                      ]);
                                    } else {
                                      setSelectedRatings(
                                        selectedRatings.filter(
                                          (r) => r !== rating
                                        )
                                      );
                                    }
                                  }}
                                />
                                <label
                                  htmlFor={`mobile-rating-${rating}`}
                                  className="text-sm cursor-pointer flex items-center gap-1"
                                >
                                  <div className="flex">
                                    {[...Array(5)].map((_, i) => (
                                      <Star
                                        key={i}
                                        className={`h-3 w-3 ${
                                          i < rating
                                            ? "fill-yellow-400 text-yellow-400"
                                            : "text-gray-300"
                                        }`}
                                      />
                                    ))}
                                  </div>
                                  & up
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Special Filters */}
                        <div>
                          <h4 className="font-medium mb-3">Special Offers</h4>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="mobile-on-sale"
                                checked={showOnSale}
                                onCheckedChange={setShowOnSale}
                              />
                              <label
                                htmlFor="mobile-on-sale"
                                className="text-sm cursor-pointer"
                              >
                                On Sale
                              </label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="mobile-in-stock"
                                checked={showInStock}
                                onCheckedChange={setShowInStock}
                              />
                              <label
                                htmlFor="mobile-in-stock"
                                className="text-sm cursor-pointer"
                              >
                                In Stock Only
                              </label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="mobile-new-arrivals"
                                checked={showNewArrivals}
                                onCheckedChange={setShowNewArrivals}
                              />
                              <label
                                htmlFor="mobile-new-arrivals"
                                className="text-sm cursor-pointer"
                              >
                                New Arrivals
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </SheetContent>
                  </Sheet>

                  {/* Sort Dropdown */}
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <ArrowUpDown className="h-4 w-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="featured">Featured</SelectItem>
                      <SelectItem value="newest">Newest</SelectItem>
                      <SelectItem value="price-low">
                        Price: Low to High
                      </SelectItem>
                      <SelectItem value="price-high">
                        Price: High to Low
                      </SelectItem>
                      <SelectItem value="rating">Highest Rated</SelectItem>
                      <SelectItem value="name">Name A-Z</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* View Mode Toggle */}
                  <div className="flex border rounded-lg">
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                      className="rounded-r-none"
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <motion.div
              layout
              className={`grid gap-6 ${
                viewMode === "grid"
                  ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                  : "grid-cols-1"
              }`}
            >
              {filteredProducts.map((prod, index) => (
                <motion.div
                  key={prod.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.05 }}
                >
                  <ProductCard product={prod} viewMode={viewMode} />
                </motion.div>
              ))}
            </motion.div>

            {/* Empty State */}
            {filteredProducts.length === 0 && (
              <div className="text-center py-16">
                <div className="text-gray-400 mb-4">
                  <Search className="h-16 w-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No products found
                </h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your filters or search terms
                </p>
                <Button onClick={clearFilters} variant="outline">
                  Clear all filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Card Component
function ProductCard({
  product,
  viewMode,
}: {
  product: Product;
  viewMode: "grid" | "list";
}) {
  const [isHovered, setIsHovered] = useState(false);

  if (viewMode === "list") {
    return (
      <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-all duration-300">
        <div className="flex">
          <div className="w-48 h-48 flex-shrink-0">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-600 mb-2">{product.category}</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  ${product.price}
                </div>
                {product.originalPrice && (
                  <div className="text-sm text-gray-500 line-through">
                    ${product.originalPrice}
                  </div>
                )}
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {product.description}
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{product.rating}</span>
                </div>

                <div className="flex gap-2">
                  {product.isNew && (
                    <Badge variant="secondary" className="text-xs">
                      New
                    </Badge>
                  )}
                  {product.isOnSale && (
                    <Badge variant="destructive" className="text-xs">
                      Sale
                    </Badge>
                  )}
                  {!product.inStock && (
                    <Badge variant="outline" className="text-xs">
                      Out of Stock
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline">
                  <Eye className="h-4 w-4" />
                </Button>
                <Link href={`/products/${product.slug}`}>
                  <Button size="sm">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Link
      href={`/products/${product.slug}`}
      className="group bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-4px] cursor-pointer border border-gray-100"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative overflow-hidden">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-64 object-cover object-center transition-transform duration-300 group-hover:scale-105"
        />

        {/* Overlay with quick actions */}
        <div
          className={`absolute inset-0 bg-black/40 flex items-center justify-center gap-2 transition-opacity duration-300 ${
            isHovered ? "opacity-100" : "opacity-0"
          }`}
        >
          <Button
            size="sm"
            variant="secondary"
            className="bg-white/90 hover:bg-white"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="bg-white/90 hover:bg-white"
          >
            <Heart className="h-4 w-4" />
          </Button>
          <Button size="sm" className="bg-black hover:bg-gray-800">
            <ShoppingBag className="h-4 w-4 mr-2" />
            Add to Cart
          </Button>
        </div>

        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-1">
          {product.isNew && (
            <Badge className="bg-green-500 hover:bg-green-600 text-white text-xs">
              New
            </Badge>
          )}
          {product.isOnSale && (
            <Badge className="bg-red-500 hover:bg-red-600 text-white text-xs">
              Sale
            </Badge>
          )}
        </div>

        {/* Stock status */}
        {!product.inStock && (
          <div className="absolute inset-0 bg-gray-900/50 flex items-center justify-center">
            <Badge variant="secondary" className="text-sm">
              Out of Stock
            </Badge>
          </div>
        )}
      </div>

      <div className="p-4">
        <div className="mb-2">
          <h3 className="font-semibold text-gray-900 group-hover:text-black transition-colors line-clamp-1">
            {product.name}
          </h3>
          <p className="text-sm text-gray-600">{product.category}</p>
        </div>

        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{product.rating}</span>
          </div>

          <div className="text-right">
            <div className="font-bold text-gray-900">${product.price}</div>
            {product.originalPrice && (
              <div className="text-sm text-gray-500 line-through">
                ${product.originalPrice}
              </div>
            )}
          </div>
        </div>

        {/* Colors */}
        {product.colors && product.colors.length > 0 && (
          <div className="flex gap-1 mb-2">
            {product.colors.slice(0, 4).map((color, index) => (
              <div
                key={index}
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: color.toLowerCase() }}
                title={color}
              />
            ))}
            {product.colors.length > 4 && (
              <span className="text-xs text-gray-500 ml-1">
                +{product.colors.length - 4}
              </span>
            )}
          </div>
        )}
      </div>
    </Link>
  );
}
