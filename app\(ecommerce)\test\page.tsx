"use client";




import React, { useState } from 'react';
import { Star, Eye, Heart, HelpCircle, Share2, Plus, Minus } from 'lucide-react';
import { Button } from '@/components/ui/button';

const ProductDetailPage = () => {
  const [quantity, setQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState('green');
  const [selectedSize, setSelectedSize] = useState('M');

  const incrementQuantity = () => setQuantity(prev => prev + 1);
  const decrementQuantity = () => setQuantity(prev => Math.max(1, prev - 1));

  const colors = ['green', 'purple', 'red', 'black'];
  const sizes = ['S', 'M', 'L', 'XL', '2XL'];

  return (
    <div className="max-w-7xl mx-auto p-8 font-sans">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Left column - Images */}
        <div className="md:w-1/2 flex">
          <div className="w-1/5 space-y-4">
            {[1, 2, 3, 4].map((num) => (
              <img key={num} src={`/cooling-gel-2023-05-12-645e043747dfe.webp`} alt={`Thumbnail ${num}`} className="w-full object-cover cursor-pointer" />
            ))}
          </div>
          <div className="w-4/5 pl-4">
            <img src="/cooling-gel-2023-05-12-645e043747dfe.webp" alt="Main product" className="w-full object-cover" />
          </div>
        </div>

        {/* Right column - Product details */}
        <div className="md:w-1/2">
          {/* Breadcrumb */}
          <nav className="text-sm mb-4">
            <ol className="list-none p-0 inline-flex">
              <li className="flex items-center text-gray-500">
                <a href="#" className="hover:text-gray-700">Home</a>
                <span className="mx-2">&gt;</span>
              </li>
              <li className="flex items-center text-gray-500">
                <a href="#" className="hover:text-gray-700">Clothing</a>
                <span className="mx-2">&gt;</span>
              </li>
              <li className="flex items-center text-gray-700">Huishō Pijama</li>
            </ol>
          </nav>

          <h1 className="text-3xl font-bold mb-4">Huishō Pijama</h1>
          <p className="text-gray-600 mb-4">
            Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt et dolore
          </p>

          {/* Reviews */}
          <div className="flex items-center mb-4">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="w-5 h-5 fill-current text-yellow-400" />
            ))}
            <span className="ml-2 text-gray-600">23 Reviews</span>
          </div>

          {/* Price */}
          <div className="mb-6">
            <span className="text-2xl font-bold">$86.00</span>
            <span className="ml-2 text-gray-500 line-through">$104.00</span>
          </div>

          {/* Viewers */}
          <div className="flex items-center text-gray-600 mb-6">
            <Eye className="w-5 h-5 mr-2" />
            <span>32 people are looking at this product</span>
          </div>

          {/* Color selection */}
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Color:</h3>
            <div className="flex space-x-2">
              {colors.map((color) => (
                <button
                  key={color}
                  className={`w-8 h-8 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    selectedColor === color ? 'ring-2 ring-offset-2 ring-gray-500' : ''
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setSelectedColor(color)}
                />
              ))}
            </div>
          </div>

          {/* Size selection */}
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Size:</h3>
            <div className="flex space-x-2">
              {sizes.map((size) => (
                <button
                  key={size}
                  className={`px-3 py-2 text-sm border rounded-md focus:outline-none ${
                    selectedSize === size
                      ? 'bg-gray-900 text-white'
                      : 'bg-white text-gray-900 hover:bg-gray-100'
                  }`}
                  onClick={() => setSelectedSize(size)}
                >
                  {size}
                </button>
              ))}
            </div>
          </div>

          {/* Quantity selector */}
          <div className="flex items-center justify-between mb-6 bg-gray-100 rounded-md w-32">
            <Button 
              onClick={decrementQuantity}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-l-md transition-colors"
            >
              <Minus size={20} />
            </Button>
            <span className="text-xl font-semibold">{quantity}</span>
            <Button 
              onClick={incrementQuantity}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-r-md transition-colors"
            >
              <Plus size={20} />
            </Button>
          </div>

          {/* Add to cart button */}
          <Button className="w-full bg-black text-white py-3 rounded-md hover:bg-gray-800 transition-colors mb-6">
            Add to Cart
          </Button>

          {/* Action buttons */}
          <div className="flex justify-between items-center text-sm text-gray-600">
            <Button variant="ghost" className="flex items-center space-x-1 hover:text-black transition-colors">
              <Heart size={20} />
              <span>Wishlist</span>
            </Button>
            <Button variant="ghost" className="flex items-center space-x-1 hover:text-black transition-colors">
              <HelpCircle size={20} />
              <span>Ask question</span>
            </Button>
            <Button variant="ghost" className="flex items-center space-x-1 hover:text-black transition-colors">
              <Share2 size={20} />
              <span>Share</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;