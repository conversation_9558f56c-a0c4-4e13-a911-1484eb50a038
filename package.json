{"name": "naffsy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "postinstall": "prisma generate"}, "dependencies": {"@auth/core": "^0.27.0", "@auth/drizzle-adapter": "^0.7.0", "@blacklab/react-image-magnify": "^4.1.1", "@hookform/resolvers": "^3.9.1", "@mantine/hooks": "^7.12.1", "@nextui-org/react": "^2.2.9", "@prisma/client": "^5.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@studio-freight/lenis": "^1.0.35", "@tanstack/react-query": "^5.21.7", "@tanstack/react-virtual": "^3.1.1", "@tiptap/pm": "^2.2.3", "@tiptap/react": "^2.2.3", "@tiptap/starter-kit": "^2.2.3", "@tremor/react": "^3.14.0", "@types/jest": "^29.5.12", "@types/node": "20.11.19", "@types/react": "18.2.56", "@types/react-dom": "18.2.19", "@vercel/speed-insights": "^1.0.10", "apexcharts": "^3.46.0", "autoprefixer": "10.4.17", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.29.3", "embla-carousel-react": "^8.3.1", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "framer-motion": "^11.0.5", "input-otp": "^1.4.1", "ioredis": "^5.3.2", "lenis": "^1.1.8", "locomotive-scroll": "^4.1.4", "lucide-react": "^0.424.0", "mysql2": "^3.9.1", "nanoid": "^5.0.5", "next": "^14.2.5", "next-auth": "^5.0.0-beta.19", "next-safe-action": "^7.6.0", "next-themes": "^0.2.1", "postcss": "8.4.35", "react": "18.2.0", "react-apexcharts": "^1.4.1", "react-aria": "^3.32.1", "react-aria-components": "^1.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.1", "react-icons": "^5.0.1", "react-lottie": "^1.2.4", "react-resizable-panels": "^2.1.6", "recharts": "^2.13.3", "sonner": "^1.7.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.7", "tailwindcss-animate": "^1.0.7", "typescript": "5.3.3", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.41.2", "@radix-ui/themes": "^2.0.3", "@tanstack/react-query-devtools": "^5.21.7", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/testing-library__jest-dom": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "drizzle-kit": "^0.20.14", "jsdom": "^24.0.0", "prisma": "^5.9.1", "vitest": "^1.3.0"}}