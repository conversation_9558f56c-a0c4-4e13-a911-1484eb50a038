"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  MessageCircle,
  X,
  History,
  MessageSquare,
  Trash2,
  Plus,
  Bot,
  Sparkles,
  Search,
  Settings,
  Download,
  Mic,
  FileUp,
  Brain,
  Zap,
  Paperclip,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import ChatTab from "./ChatTab";
import FAQTab from "./FAQTab";

// Types
export type Message = {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: Date;
  attachments?: Attachment[];
  reactions?: Reaction[];
  isEdited?: boolean;
};

export type Attachment = {
  id: string;
  type: "image" | "file" | "audio";
  url: string;
  name: string;
  size?: number;
};

export type Reaction = {
  emoji: string;
  count: number;
  users: string[];
};

export type ChatHistory = {
  id: string;
  title: string;
  messages: Message[];
  date: Date;
  aiModel?: string;
  contextLength?: number;
};

export type AIModel = {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  contextLength: number;
};

export type ChatSettings = {
  aiModel: string;
  voiceInput: boolean;
  notifications: boolean;
  darkMode: boolean;
  fontSize: number;
  messageSound: boolean;
};

// AI Models available
const aiModels: AIModel[] = [
  {
    id: "default",
    name: "Standard Assistant",
    description: "General-purpose AI assistant for everyday questions",
    icon: Bot,
    contextLength: 4000,
  },
  {
    id: "advanced",
    name: "Advanced AI",
    description: "More powerful model with enhanced reasoning capabilities",
    icon: Brain,
    contextLength: 8000,
  },
  {
    id: "expert",
    name: "Expert Assistant",
    description: "Specialized knowledge for complex queries",
    icon: Sparkles,
    contextLength: 16000,
  },
  {
    id: "turbo",
    name: "Turbo Response",
    description: "Optimized for speed with shorter responses",
    icon: Zap,
    contextLength: 2000,
  },
];

// Default settings
const defaultSettings: ChatSettings = {
  aiModel: "default",
  voiceInput: true,
  notifications: true,
  darkMode: false,
  fontSize: 14,
  messageSound: true,
};

const ChatbotWidget = () => {
  // State
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("chat");
  const [showHistory, setShowHistory] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [currentChat, setCurrentChat] = useState<Message[]>([]);
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [settings, setSettings] = useState<ChatSettings>(defaultSettings);
  const [isRecording, setIsRecording] = useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>("default");

  // Refs
  const widgetRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Close widget when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        widgetRef.current &&
        !widgetRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Load chat history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem("chatHistory");
    if (savedHistory) {
      try {
        const parsedHistory = JSON.parse(savedHistory);
        // Convert string dates back to Date objects
        const formattedHistory = parsedHistory.map((chat: any) => ({
          ...chat,
          date: new Date(chat.date),
          messages: chat.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }));
        setChatHistory(formattedHistory);
      } catch (error) {
        console.error("Failed to parse chat history:", error);
      }
    }
  }, []);

  // Save chat history to localStorage when it changes
  useEffect(() => {
    if (chatHistory.length > 0) {
      localStorage.setItem("chatHistory", JSON.stringify(chatHistory));
    }
  }, [chatHistory]);

  // Toggle the chatbot widget
  const toggleWidget = () => {
    setIsOpen(!isOpen);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Toggle history view
  const toggleHistory = () => {
    setShowHistory(!showHistory);
  };

  // Start a new chat
  const startNewChat = () => {
    setCurrentChat([]);
    setShowHistory(false);
  };

  // Load a chat from history
  const loadChat = (chatId: string) => {
    const chat = chatHistory.find((c) => c.id === chatId);
    if (chat) {
      setCurrentChat(chat.messages);
      setShowHistory(false);
      setActiveTab("chat");
    }
  };

  // Save current chat to history
  const saveChat = () => {
    if (currentChat.length === 0) return;

    const newChatHistory = [...chatHistory];
    const chatTitle = currentChat[0]?.content.slice(0, 20) + "...";

    const newChat: ChatHistory = {
      id: Date.now().toString(),
      title: chatTitle,
      messages: [...currentChat],
      date: new Date(),
    };

    newChatHistory.push(newChat);
    setChatHistory(newChatHistory);
  };

  // Send a message
  const sendMessage = (content: string) => {
    if (!content.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: "user",
      timestamp: new Date(),
    };

    const updatedChat = [...currentChat, newMessage];
    setCurrentChat(updatedChat);

    // Simulate bot typing
    setIsTyping(true);

    // Simulate bot response after a delay
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: getBotResponse(content),
        sender: "bot",
        timestamp: new Date(),
      };

      setCurrentChat([...updatedChat, botResponse]);
      setIsTyping(false);

      // Save chat to history if this is the first exchange
      if (updatedChat.length === 1) {
        setTimeout(() => saveChat(), 500);
      }
    }, 1500);
  };

  // Advanced bot response generator
  const getBotResponse = (message: string): string => {
    const lowerMessage = message.toLowerCase();
    const currentModel =
      aiModels.find((model) => model.id === selectedModel) || aiModels[0];

    // Greeting patterns
    if (lowerMessage.match(/\b(hello|hi|hey|greetings|howdy)\b/i)) {
      const greetings = [
        `Hello! I'm your ${currentModel.name}. How can I assist you today?`,
        `Hi there! I'm here to help with any questions about our products or services.`,
        `Hey! Thanks for reaching out. What can I help you with today?`,
        `Greetings! I'm your AI shopping assistant. How may I help you?`,
      ];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }

    // Help patterns
    else if (lowerMessage.match(/\b(help|assist|support)\b/i)) {
      return `I'm here to help! With the ${currentModel.name}, I can assist with:
1. Product recommendations and information
2. Order tracking and shipping details
3. Return and refund policies
4. Account management
5. Technical support

What specific area do you need help with?`;
    }

    // Product patterns
    else if (
      lowerMessage.match(/\b(product|item|catalog|collection|merchandise)\b/i)
    ) {
      return `We offer a wide range of products in our catalog. Our most popular categories include:

• Skincare: Cleansers, moisturizers, serums, and treatments
• Makeup: Foundations, lipsticks, eye products, and brushes
• Haircare: Shampoos, conditioners, styling products
• Fragrance: Perfumes, colognes, and body sprays
• Bath & Body: Soaps, lotions, and bath accessories

Would you like me to recommend something specific based on your needs?`;
    }

    // Shipping patterns
    else if (
      lowerMessage.match(
        /\b(shipping|delivery|ship|deliver|track|order status)\b/i
      )
    ) {
      return `Here's our complete shipping information:

• Free standard shipping on all orders over $50
• Standard delivery (3-5 business days): $4.99
• Express delivery (1-2 business days): $9.99
• Next-day delivery (order by 2pm): $14.99
• International shipping available to 30+ countries

You can track your order anytime in your account dashboard or by clicking the tracking link in your confirmation email. Need help with a specific order?`;
    }

    // Return patterns
    else if (
      lowerMessage.match(/\b(return|refund|exchange|money back|cancel)\b/i)
    ) {
      return `Our return policy includes:

• 30-day satisfaction guarantee on all products
• Free returns with prepaid shipping labels
• Full refunds processed within 5-7 business days
• Store credit option with 10% bonus value
• Exchanges available for different sizes/colors

To initiate a return, visit your order history in your account or contact our customer service team. Would you like me to guide you through the return process?`;
    }

    // Account patterns
    else if (
      lowerMessage.match(/\b(account|login|password|sign in|register)\b/i)
    ) {
      return `I can help with your account! Here are some common account-related actions:

• Create a new account: Click the profile icon in the top right
• Reset password: Use the "Forgot Password" link on the login page
• Update account details: Go to Account Settings after logging in
• View order history: Check the Orders tab in your account
• Manage payment methods: Visit the Payment section

Is there something specific about your account I can help with?`;
    }

    // Pricing patterns
    else if (
      lowerMessage.match(/\b(price|cost|discount|sale|coupon|promo|offer)\b/i)
    ) {
      return `We have several ongoing promotions:

• 15% off your first order with code WELCOME15
• Free shipping on orders over $50
• Buy 2, get 1 free on select items (marked with special tags)
• Seasonal sales with up to 40% off
• Loyalty program with points on every purchase

Would you like me to check if any specific products are on sale?`;
    }

    // Fallback response
    else {
      return `I'm not sure I fully understand your question. As the ${currentModel.name}, I'm designed to help with product information, orders, shipping, returns, and account questions. Could you please rephrase or provide more details about what you're looking for?`;
    }
  };

  // Handle file attachment
  const handleFileAttachment = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Process file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // In a real implementation, you would upload these files to a server
    // and get back URLs. For this demo, we'll create fake URLs
    const newAttachments: Attachment[] = Array.from(files).map((file) => ({
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
      type: file.type.startsWith("image/")
        ? "image"
        : file.type.startsWith("audio/")
        ? "audio"
        : "file",
      url: URL.createObjectURL(file), // This creates a temporary local URL
      name: file.name,
      size: file.size,
    }));

    setAttachments([...attachments, ...newAttachments]);
  };

  // Toggle voice recording
  const toggleVoiceRecording = () => {
    // In a real implementation, you would use the Web Speech API or similar
    setIsRecording(!isRecording);

    if (!isRecording) {
      // Start recording logic would go here
      // For demo purposes, we'll just set a timeout to simulate recording
      setTimeout(() => {
        setIsRecording(false);
        // Simulate transcribed text
        const transcribedText =
          "This is a simulated voice message transcription.";
        sendMessage(transcribedText);
      }, 3000);
    } else {
      // Stop recording logic would go here
    }
  };

  // Export chat history
  const exportChat = () => {
    if (currentChat.length === 0) return;

    const chatData = {
      title: currentChat[0]?.content.slice(0, 20) + "...",
      date: new Date().toISOString(),
      messages: currentChat.map((msg) => ({
        content: msg.content,
        sender: msg.sender,
        timestamp: msg.timestamp.toISOString(),
      })),
    };

    const dataStr = JSON.stringify(chatData, null, 2);
    const dataUri =
      "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);

    const exportFileDefaultName = `chat-export-${new Date()
      .toISOString()
      .slice(0, 10)}.json`;

    const linkElement = document.createElement("a");
    linkElement.setAttribute("href", dataUri);
    linkElement.setAttribute("download", exportFileDefaultName);
    linkElement.click();
  };

  return (
    <div ref={widgetRef} className="fixed bottom-6 right-6 z-50">
      {/* Chatbot Icon Button */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={toggleWidget}
              className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:shadow-xl transition-all duration-300 border-0"
              size="icon"
            >
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                  repeatDelay: 5,
                }}
              >
                <MessageCircle className="h-6 w-6 text-white" />
              </motion.div>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Chat with Forever Assistant</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Chatbot Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed md:absolute bottom-0 md:bottom-20 right-0 left-0 md:left-auto w-full md:w-[400px] h-[85vh] md:h-[550px] bg-background rounded-t-xl md:rounded-xl shadow-2xl border overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8 border-2 border-primary/20">
                  <AvatarImage src="/bot-avatar.png" alt="Forever Assistant" />
                  <AvatarFallback className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    Forever Assistant
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Badge
                          variant="outline"
                          className="ml-1 bg-primary/5 text-xs font-normal cursor-pointer hover:bg-primary/10 transition-colors"
                        >
                          {aiModels.find((model) => model.id === selectedModel)
                            ?.name || "Standard Assistant"}
                          <Sparkles className="h-3 w-3 ml-1 text-amber-500" />
                        </Badge>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start" className="w-56">
                        <DropdownMenuLabel>Select AI Model</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {aiModels.map((model) => (
                          <DropdownMenuItem
                            key={model.id}
                            onClick={() => setSelectedModel(model.id)}
                            className={cn(
                              "flex items-center gap-2 cursor-pointer",
                              selectedModel === model.id &&
                                "bg-primary/10 font-medium"
                            )}
                          >
                            <div className="p-1 rounded-full bg-primary/10">
                              <model.icon className="h-3 w-3 text-primary" />
                            </div>
                            <div className="flex flex-col">
                              <span>{model.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {model.description}
                              </span>
                            </div>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </h3>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={toggleHistory}
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "h-8 w-8 rounded-full",
                          showHistory && "bg-accent"
                        )}
                      >
                        <History className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Chat history</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={() => setShowSettings(!showSettings)}
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "h-8 w-8 rounded-full",
                          showSettings && "bg-accent"
                        )}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Settings</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={toggleWidget}
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-full"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Close</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            {/* Content */}
            <div className="h-[calc(100%-64px)]">
              {showSettings ? (
                <div className="h-full overflow-y-auto p-4">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="font-medium text-lg">Settings</h4>
                    <Badge variant="outline" className="bg-primary/5 px-2 py-1">
                      <Settings className="h-3 w-3 mr-1 text-indigo-500" />
                      Preferences
                    </Badge>
                  </div>

                  <div className="space-y-6">
                    {/* AI Model Selection */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm text-muted-foreground">
                        AI MODEL
                      </h5>
                      <div className="grid gap-4">
                        {aiModels.map((model) => (
                          <div
                            key={model.id}
                            className={cn(
                              "flex items-start gap-3 p-3 rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors",
                              settings.aiModel === model.id &&
                                "border-primary bg-primary/5"
                            )}
                            onClick={() =>
                              setSettings({ ...settings, aiModel: model.id })
                            }
                          >
                            <div
                              className={cn(
                                "p-2 rounded-full",
                                settings.aiModel === model.id
                                  ? "bg-primary text-white"
                                  : "bg-muted"
                              )}
                            >
                              <model.icon className="h-4 w-4" />
                            </div>
                            <div className="space-y-1">
                              <div className="font-medium text-sm">
                                {model.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {model.description}
                              </div>
                              <div className="text-xs text-primary">
                                Context: {model.contextLength.toLocaleString()}{" "}
                                tokens
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Interface Settings */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm text-muted-foreground">
                        INTERFACE
                      </h5>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="dark-mode" className="text-sm">
                              Dark Mode
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              Switch between light and dark theme
                            </p>
                          </div>
                          <Switch
                            id="dark-mode"
                            checked={settings.darkMode}
                            onCheckedChange={(checked) =>
                              setSettings({ ...settings, darkMode: checked })
                            }
                          />
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="font-size" className="text-sm">
                              Font Size
                            </Label>
                            <span className="text-xs text-muted-foreground">
                              {settings.fontSize}px
                            </span>
                          </div>
                          <Slider
                            id="font-size"
                            min={12}
                            max={20}
                            step={1}
                            value={[settings.fontSize]}
                            onValueChange={(value) =>
                              setSettings({ ...settings, fontSize: value[0] })
                            }
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Features Settings */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm text-muted-foreground">
                        FEATURES
                      </h5>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="voice-input" className="text-sm">
                              Voice Input
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              Enable voice message recording
                            </p>
                          </div>
                          <Switch
                            id="voice-input"
                            checked={settings.voiceInput}
                            onCheckedChange={(checked) =>
                              setSettings({ ...settings, voiceInput: checked })
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="notifications" className="text-sm">
                              Notifications
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              Show notifications for new messages
                            </p>
                          </div>
                          <Switch
                            id="notifications"
                            checked={settings.notifications}
                            onCheckedChange={(checked) =>
                              setSettings({
                                ...settings,
                                notifications: checked,
                              })
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="message-sound" className="text-sm">
                              Message Sound
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              Play sound when messages are received
                            </p>
                          </div>
                          <Switch
                            id="message-sound"
                            checked={settings.messageSound}
                            onCheckedChange={(checked) =>
                              setSettings({
                                ...settings,
                                messageSound: checked,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <div className="pt-4 flex justify-end">
                      <Button
                        onClick={() => setShowSettings(false)}
                        className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                      >
                        Save Settings
                      </Button>
                    </div>
                  </div>
                </div>
              ) : showHistory ? (
                <div className="h-full overflow-y-auto p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-lg">Chat History</h4>
                    {chatHistory.length > 0 && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              onClick={() => {
                                if (
                                  confirm(
                                    "Are you sure you want to clear all chat history?"
                                  )
                                ) {
                                  setChatHistory([]);
                                  localStorage.removeItem("chatHistory");
                                }
                              }}
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive hover:bg-destructive/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="left">
                            <p>Clear all history</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>

                  {chatHistory.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[80%] text-center">
                      <div className="bg-muted p-6 rounded-full mb-4">
                        <History className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="font-medium text-lg">
                        No chat history yet
                      </h3>
                      <p className="text-muted-foreground text-sm mt-2 max-w-[250px]">
                        Your conversations will appear here once you start
                        chatting.
                      </p>
                      <Button onClick={startNewChat} className="mt-6">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Start a new chat
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Button
                        onClick={startNewChat}
                        variant="default"
                        className="w-full justify-start bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 transition-all duration-300"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Start new chat
                      </Button>

                      <div className="mt-4 space-y-2">
                        {chatHistory.map((chat) => (
                          <div key={chat.id} className="group relative">
                            <Button
                              onClick={() => loadChat(chat.id)}
                              variant="ghost"
                              className="w-full justify-start rounded-lg p-3 h-auto"
                            >
                              <div className="flex items-start gap-3">
                                <div className="bg-primary/10 p-2 rounded-full">
                                  <MessageSquare className="h-4 w-4 text-primary" />
                                </div>
                                <div className="flex flex-col items-start text-left">
                                  <span className="font-medium text-sm truncate max-w-[200px]">
                                    {chat.title}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {chat.date.toLocaleDateString()} ·{" "}
                                    {chat.messages.length} messages
                                  </span>
                                </div>
                              </div>
                            </Button>
                            <Button
                              onClick={() => {
                                const updatedHistory = chatHistory.filter(
                                  (c) => c.id !== chat.id
                                );
                                setChatHistory(updatedHistory);
                              }}
                              variant="ghost"
                              size="icon"
                              className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <Trash2 className="h-3 w-3 text-muted-foreground" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <Tabs
                  value={activeTab}
                  onValueChange={handleTabChange}
                  className="h-full"
                >
                  <TabsList className="w-full justify-start px-4 pt-3 pb-1 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 border-b h-auto gap-4">
                    <TabsTrigger
                      value="chat"
                      className="data-[state=active]:bg-transparent data-[state=active]:text-indigo-700 data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-indigo-500 rounded-none px-2 pb-2 font-medium"
                    >
                      <MessageCircle className="mr-2 h-4 w-4" />
                      Chat
                    </TabsTrigger>
                    <TabsTrigger
                      value="faq"
                      className="data-[state=active]:bg-transparent data-[state=active]:text-indigo-700 data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-indigo-500 rounded-none px-2 pb-2 font-medium"
                    >
                      <Search className="mr-2 h-4 w-4" />
                      FAQ
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="chat" className="h-[calc(100%-40px)] m-0">
                    <ChatTab
                      messages={currentChat}
                      onSendMessage={sendMessage}
                      isTyping={isTyping}
                      voiceInputEnabled={settings.voiceInput}
                    />
                  </TabsContent>
                  <TabsContent value="faq" className="h-[calc(100%-40px)] m-0">
                    <FAQTab onAskQuestion={sendMessage} />
                  </TabsContent>
                </Tabs>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChatbotWidget;
