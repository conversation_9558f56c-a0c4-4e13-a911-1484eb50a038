"use client";

import * as React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  ArrowRight,
  Star,
  TrendingUp,
  Zap,
  ShoppingBag,
  Heart,
  Sparkles,
  Crown,
  Target,
  Mountain,
  Dumbbell,
  Coffee,
  Flame,
  Award,
  Users,
  Percent,
  Grid3X3,
  Layers,
  Palette,
  Rocket,
  Diamond,
  Lightbulb,
  Headphones,
  Shield,
  Truck,
  RotateCcw,
  CreditCard,
} from "lucide-react";

import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

// Advanced mega menu data structure
const megaMenuData = {
  shop: {
    categories: [
      {
        id: "performance",
        name: "Performance",
        description: "Elite athletic footwear",
        icon: Rocket,
        gradient: "from-violet-600 via-purple-600 to-blue-600",
        items: [
          {
            name: "Running Shoes",
            href: "/categories/running",
            icon: Target,
            count: 24,
          },
          {
            name: "Training Shoes",
            href: "/categories/training",
            icon: Dumbbell,
            count: 18,
          },
          {
            name: "Basketball",
            href: "/categories/basketball",
            icon: Award,
            count: 15,
          },
          {
            name: "Cross Training",
            href: "/categories/cross-training",
            icon: Zap,
            count: 12,
          },
        ],
      },
      {
        id: "lifestyle",
        name: "Lifestyle",
        description: "Everyday comfort & style",
        icon: Sparkles,
        gradient: "from-pink-500 via-rose-500 to-orange-500",
        items: [
          {
            name: "Casual Sneakers",
            href: "/categories/casual",
            icon: Coffee,
            count: 32,
          },
          {
            name: "Street Style",
            href: "/categories/street",
            icon: Palette,
            count: 28,
          },
          {
            name: "Comfort Wear",
            href: "/categories/comfort",
            icon: Heart,
            count: 22,
          },
          {
            name: "Fashion Forward",
            href: "/categories/fashion",
            icon: Crown,
            count: 19,
          },
        ],
      },
      {
        id: "outdoor",
        name: "Outdoor",
        description: "Adventure-ready gear",
        icon: Mountain,
        gradient: "from-green-600 via-emerald-600 to-teal-600",
        items: [
          {
            name: "Hiking Boots",
            href: "/categories/hiking",
            icon: Mountain,
            count: 16,
          },
          {
            name: "Trail Running",
            href: "/categories/trail",
            icon: Layers,
            count: 14,
          },
          {
            name: "Waterproof",
            href: "/categories/waterproof",
            icon: Shield,
            count: 11,
          },
          {
            name: "All-Terrain",
            href: "/categories/all-terrain",
            icon: Grid3X3,
            count: 9,
          },
        ],
      },
    ],
    featured: [
      {
        id: 1,
        name: "AirNags Quantum Pro",
        price: 299,
        originalPrice: 399,
        image: "/656.png",
        href: "/products/quantum-pro",
        badge: "🔥 Hot",
        rating: 4.9,
        reviews: 2847,
        description: "Revolutionary cushioning technology",
        colors: ["#000", "#fff", "#ff6b6b", "#4ecdc4"],
        isNew: true,
        discount: 25,
      },
      {
        id: 2,
        name: "Urban Elite X1",
        price: 199,
        originalPrice: 249,
        image: "/564.webp",
        href: "/products/urban-elite-x1",
        badge: "⭐ Bestseller",
        rating: 4.8,
        reviews: 1923,
        description: "Premium street-ready design",
        colors: ["#2c3e50", "#e74c3c", "#f39c12"],
        isNew: false,
        discount: 20,
      },
      {
        id: 3,
        name: "Trail Master Pro",
        price: 249,
        image: "/642.webp",
        href: "/products/trail-master-pro",
        badge: "💎 Limited",
        rating: 4.9,
        reviews: 856,
        description: "Ultimate outdoor performance",
        colors: ["#27ae60", "#8e44ad", "#34495e"],
        isNew: true,
        discount: 0,
      },
    ],
    trending: [
      { name: "Sustainable Collection", icon: Lightbulb, trend: "+45%" },
      { name: "Limited Editions", icon: Diamond, trend: "+32%" },
      { name: "Collaborations", icon: Users, trend: "+28%" },
    ],
    services: [
      { name: "Free Shipping", icon: Truck, description: "On orders over $75" },
      {
        name: "Easy Returns",
        icon: RotateCcw,
        description: "30-day guarantee",
      },
      {
        name: "Expert Support",
        icon: Headphones,
        description: "24/7 assistance",
      },
      {
        name: "Secure Payment",
        icon: CreditCard,
        description: "Protected checkout",
      },
    ],
  },
  collections: [
    {
      id: "new-arrivals",
      name: "New Arrivals",
      description: "Fresh drops every week",
      icon: Sparkles,
      gradient: "from-indigo-500 to-purple-600",
      image: "/656.png",
      count: 47,
      isHot: true,
    },
    {
      id: "bestsellers",
      name: "Bestsellers",
      description: "Customer favorites",
      icon: Award,
      gradient: "from-yellow-500 to-orange-600",
      image: "/564.webp",
      count: 23,
      isHot: false,
    },
    {
      id: "limited-edition",
      name: "Limited Edition",
      description: "Exclusive releases",
      icon: Crown,
      gradient: "from-rose-500 to-pink-600",
      image: "/642.webp",
      count: 12,
      isHot: true,
    },
  ],
  sale: {
    flashSale: {
      title: "Flash Sale",
      subtitle: "Ends in 2h 34m",
      discount: "Up to 60% OFF",
      gradient: "from-red-500 to-pink-600",
      icon: Flame,
      products: 156,
    },
    seasonal: {
      title: "Season End Sale",
      subtitle: "Limited time only",
      discount: "40% OFF",
      gradient: "from-orange-500 to-red-600",
      icon: Percent,
      products: 89,
    },
  },
};

export default function NavMenuStore() {
  return (
    <NavigationMenu className="relative z-10">
      <NavigationMenuList className="gap-1">
        {/* Shop Mega Menu */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className="text-sm font-medium bg-transparent hover:bg-gray-100/80 data-[state=open]:bg-gray-100/80 rounded-lg">
            Shop
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="w-[1000px] p-0 overflow-hidden rounded-2xl shadow-xl border border-gray-100 bg-white">
              <div className="grid grid-cols-12 gap-0">
                {/* Categories Section - Enhanced */}
                <div className="col-span-4 p-6 bg-gradient-to-b from-gray-50/80 to-white">
                  <div className="mb-5">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1.5 flex items-center gap-2">
                      <div className="p-1.5 rounded-lg bg-indigo-100 text-indigo-600">
                        <ShoppingBag className="h-4 w-4" />
                      </div>
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-violet-600">
                        Shop by Category
                      </span>
                    </h3>
                    <p className="text-sm text-gray-600 font-normal">
                      Discover our premium collections
                    </p>
                  </div>
                  <div className="grid gap-2.5">
                    {megaMenuData.shop.categories.map((category) => {
                      const IconComponent = category.icon;
                      return (
                        <NavigationMenuLink key={category.id} asChild>
                          <Link
                            href={category.items[0]?.href || "#"}
                            className="group flex items-center gap-3 p-3 rounded-xl hover:bg-white hover:shadow-md transition-all duration-300 border border-transparent hover:border-gray-100"
                          >
                            <div
                              className={`p-2.5 rounded-lg bg-gradient-to-r ${category.gradient} text-white shadow-sm group-hover:shadow-md transition-all duration-300 group-hover:scale-110 relative overflow-hidden`}
                            >
                              <div className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.4),transparent)]"></div>
                              <IconComponent className="h-4 w-4 relative z-10" />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-gray-900 group-hover:text-indigo-600 transition-colors">
                                {category.name}
                              </div>
                              <div className="text-xs text-gray-500 mt-0.5 flex items-center gap-1">
                                <span className="inline-block w-1.5 h-1.5 rounded-full bg-indigo-500/40"></span>
                                {category.items.reduce(
                                  (sum, item) => sum + item.count,
                                  0
                                )}{" "}
                                products
                              </div>
                            </div>
                            <div className="h-8 w-8 flex items-center justify-center rounded-full bg-gray-50 group-hover:bg-indigo-50 transition-colors">
                              <ArrowRight className="h-3.5 w-3.5 text-gray-400 group-hover:text-indigo-600 group-hover:translate-x-0.5 transition-all" />
                            </div>
                          </Link>
                        </NavigationMenuLink>
                      );
                    })}
                  </div>
                </div>

                {/* Featured Products Section - Enhanced */}
                <div className="col-span-5 p-6 border-l border-gray-100 bg-white">
                  <div className="mb-5">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1.5 flex items-center gap-2">
                      <div className="p-1.5 rounded-lg bg-amber-100 text-amber-600">
                        <Star className="h-4 w-4" />
                      </div>
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-amber-500 to-orange-500">
                        Featured Products
                      </span>
                    </h3>
                    <p className="text-sm text-gray-600 font-normal">
                      Our most popular items
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    {megaMenuData.shop.featured.map((product) => (
                      <NavigationMenuLink key={product.id} asChild>
                        <Link
                          href={product.href}
                          className="group flex flex-col rounded-xl overflow-hidden border border-gray-100 hover:border-gray-200 hover:shadow-lg transition-all duration-300"
                        >
                          <div className="relative w-full aspect-[4/3] overflow-hidden bg-gray-50">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <Badge
                              className="absolute top-2 right-2 text-[10px] px-2 py-0.5 shadow-sm"
                              variant={
                                product.badge.includes("Hot")
                                  ? "destructive"
                                  : product.badge.includes("Bestseller")
                                  ? "secondary"
                                  : "outline"
                              }
                            >
                              {product.badge}
                            </Badge>
                            <div className="absolute bottom-2 left-2 right-2 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-y-2 group-hover:translate-y-0">
                              <Badge className="bg-white/90 text-gray-800 hover:bg-white border-0 backdrop-blur-sm text-[10px]">
                                {product.colors.length} colors
                              </Badge>
                              <Badge className="bg-white/90 text-indigo-600 hover:bg-white border-0 backdrop-blur-sm text-[10px]">
                                Quick view
                              </Badge>
                            </div>
                          </div>
                          <div className="p-3">
                            <div className="font-medium text-sm text-gray-900 group-hover:text-indigo-600 transition-colors line-clamp-1">
                              {product.name}
                            </div>
                            <div className="flex items-center gap-2 mt-1.5">
                              <span className="text-sm font-semibold text-gray-900">
                                ${product.price}
                              </span>
                              {product.originalPrice && (
                                <span className="text-xs text-gray-500 line-through">
                                  ${product.originalPrice}
                                </span>
                              )}
                              {product.discount > 0 && (
                                <span className="text-xs font-medium bg-green-100 text-green-800 px-1.5 py-0.5 rounded-full">
                                  {product.discount}% off
                                </span>
                              )}
                            </div>
                            <div className="flex items-center justify-between mt-2">
                              <div className="flex items-center gap-1">
                                <div className="flex">
                                  {[...Array(5)].map((_, i) => (
                                    <Star
                                      key={i}
                                      className={`h-3 w-3 ${
                                        i < Math.floor(product.rating)
                                          ? "fill-amber-400 text-amber-400"
                                          : "text-gray-300"
                                      }`}
                                    />
                                  ))}
                                </div>
                                <span className="text-xs text-gray-500 ml-1">
                                  ({product.reviews})
                                </span>
                              </div>
                              <div className="flex gap-1">
                                {product.colors.slice(0, 3).map((color, i) => (
                                  <div
                                    key={i}
                                    className="w-2.5 h-2.5 rounded-full border border-gray-200"
                                    style={{ backgroundColor: color }}
                                  ></div>
                                ))}
                                {product.colors.length > 3 && (
                                  <div className="w-2.5 h-2.5 rounded-full bg-gray-100 flex items-center justify-center text-[6px] text-gray-500">
                                    +{product.colors.length - 3}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </Link>
                      </NavigationMenuLink>
                    ))}
                  </div>
                </div>

                {/* Promotional Section - Enhanced */}
                <div className="col-span-3 overflow-hidden relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 via-violet-600 to-purple-700"></div>
                  <div className="absolute inset-0 opacity-30 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjAzKSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==')]"></div>
                  <div className="absolute inset-0 opacity-40 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.25),transparent),radial-gradient(circle_at_70%_65%,rgba(255,255,255,0.2),transparent)]"></div>
                  <div className="relative h-full p-6 flex flex-col justify-between z-10">
                    <div>
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        className="inline-flex items-center gap-1.5 px-3 py-1.5 mb-4 bg-white/20 backdrop-blur-sm text-white text-xs font-medium rounded-full"
                      >
                        <Sparkles className="h-3.5 w-3.5 animate-pulse" />
                        <span>Special Offer</span>
                      </motion.div>
                      <h3 className="text-xl font-bold text-white mb-2">
                        Premium Collection
                      </h3>
                      <p className="text-sm text-indigo-100 mb-6 line-clamp-3">
                        Discover our exclusive premium collection with special
                        discounts for limited time!
                      </p>
                      <div className="flex items-center gap-2 mb-4">
                        <div className="flex -space-x-2">
                          {[...Array(3)].map((_, i) => (
                            <div
                              key={i}
                              className="w-6 h-6 rounded-full border-2 border-white overflow-hidden"
                            >
                              <div
                                className={`w-full h-full bg-gradient-to-r ${
                                  i === 0
                                    ? "from-pink-500 to-rose-500"
                                    : i === 1
                                    ? "from-blue-500 to-indigo-500"
                                    : "from-amber-500 to-orange-500"
                                }`}
                              ></div>
                            </div>
                          ))}
                        </div>
                        <span className="text-xs text-white/90">
                          +12 more colors
                        </span>
                      </div>
                    </div>
                    <Button
                      className="w-full bg-white hover:bg-gray-50 text-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden group"
                      size="sm"
                    >
                      <span className="relative z-10">Explore Collection</span>
                      <span className="absolute inset-0 bg-gradient-to-r from-indigo-100 to-violet-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    </Button>
                  </div>
                </div>
              </div>

              {/* Trending Section - New */}
              <div className="grid grid-cols-4 gap-4 px-6 py-4 bg-gradient-to-r from-gray-50/50 to-white border-t border-gray-100">
                <div className="col-span-4 mb-2">
                  <h4 className="text-sm font-medium text-gray-900 flex items-center gap-1.5">
                    <TrendingUp className="h-3.5 w-3.5 text-indigo-600" />
                    <span>Trending Now</span>
                  </h4>
                </div>
                {megaMenuData.shop.trending.map((trend, i) => {
                  const TrendIcon = trend.icon;
                  return (
                    <div
                      key={i}
                      className="flex items-center gap-2 p-2 rounded-lg bg-white border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200 hover:border-indigo-100 group"
                    >
                      <div className="p-1.5 rounded-md bg-gray-50 group-hover:bg-indigo-50 transition-colors">
                        <TrendIcon className="h-3.5 w-3.5 text-gray-500 group-hover:text-indigo-600 transition-colors" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs font-medium text-gray-900 group-hover:text-indigo-600 transition-colors">
                          {trend.name}
                        </span>
                        <span className="text-[10px] text-green-600 font-medium flex items-center gap-0.5">
                          <TrendingUp className="h-2.5 w-2.5" />
                          {trend.trend}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Footer section with services - Enhanced */}
              <div className="grid grid-cols-4 gap-2 p-3 bg-gray-900/95 border-t border-gray-800">
                {megaMenuData.shop.services.map((service, i) => {
                  const IconComponent = service.icon;
                  return (
                    <div key={i} className="flex items-center gap-2 p-2">
                      <div className="p-1.5 rounded-full bg-indigo-500/20">
                        <IconComponent className="h-3.5 w-3.5 text-indigo-300" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs font-medium text-white">
                          {service.name}
                        </span>
                        <span className="text-[10px] text-gray-400">
                          {service.description}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* Categories Menu */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className="text-sm font-medium bg-transparent hover:bg-gray-100/80 data-[state=open]:bg-gray-100/80 rounded-lg">
            Categories
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="w-[700px] p-0 overflow-hidden rounded-2xl shadow-xl border border-gray-100 bg-white">
              <div className="p-6 bg-gradient-to-b from-gray-50/80 to-white">
                <div className="mb-5">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1.5 flex items-center gap-2">
                    <div className="p-1.5 rounded-lg bg-indigo-100 text-indigo-600">
                      <Grid3X3 className="h-4 w-4" />
                    </div>
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-violet-600">
                      Browse Collections
                    </span>
                  </h3>
                  <p className="text-sm text-gray-600 font-normal">
                    Find the perfect style for every occasion
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {megaMenuData.collections.map((category) => {
                    const IconComponent = category.icon;
                    return (
                      <NavigationMenuLink key={category.id} asChild>
                        <Link
                          href={`/collections/${category.id}`}
                          className="group relative overflow-hidden rounded-xl border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-lg"
                        >
                          <div className="aspect-video relative">
                            <img
                              src={category.image}
                              alt={category.name}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                            {category.isHot && (
                              <motion.div
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.2 }}
                                className="absolute top-2 right-2 px-2 py-1 bg-gradient-to-r from-red-500 to-rose-500 backdrop-blur-sm text-white text-xs font-medium rounded-full shadow-md"
                              >
                                <span className="flex items-center gap-1">
                                  <Flame className="h-3 w-3" />
                                  Trending
                                </span>
                              </motion.div>
                            )}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-80 group-hover:opacity-90 transition-opacity" />
                            <div className="absolute bottom-4 left-4 right-4">
                              <div className="flex items-center gap-2 mb-2">
                                <div
                                  className={`p-1.5 rounded-md bg-gradient-to-r ${category.gradient} text-white shadow-sm relative overflow-hidden group-hover:scale-110 transition-transform duration-300`}
                                >
                                  <div className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.4),transparent)]"></div>
                                  <IconComponent className="h-4 w-4 relative z-10" />
                                </div>
                                <Badge
                                  variant="secondary"
                                  className="text-[10px] bg-white/90 backdrop-blur-sm shadow-sm"
                                >
                                  {category.count} items
                                </Badge>
                              </div>
                              <h4 className="font-semibold text-white mb-1 group-hover:text-indigo-100 transition-colors">
                                {category.name}
                              </h4>
                              <p className="text-xs text-gray-200 line-clamp-1">
                                {category.description}
                              </p>
                            </div>
                          </div>
                        </Link>
                      </NavigationMenuLink>
                    );
                  })}
                </div>
              </div>

              <div className="px-6 py-4 bg-gray-900/95 border-t border-gray-800">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-gray-300">
                    Browse all collections in our catalog
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1.5 bg-white/10 text-white border-white/20 hover:bg-white/20 hover:text-white"
                  >
                    View all categories
                    <ArrowRight className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* New Arrivals */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className="text-sm font-medium bg-transparent hover:bg-gray-100/80 data-[state=open]:bg-gray-100/80 rounded-lg">
            <span className="flex items-center gap-1.5">
              New Arrivals
              <Badge className="ml-0.5 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded-full px-1.5 py-0 h-4 text-[10px]">
                New
              </Badge>
            </span>
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="w-[600px] p-0 overflow-hidden rounded-2xl shadow-xl border border-gray-100 bg-white">
              <div className="p-6 bg-gradient-to-b from-gray-50/80 to-white">
                <div className="mb-5">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 rounded-lg bg-green-100 text-green-700">
                        <TrendingUp className="h-4 w-4" />
                      </div>
                      <h3 className="text-lg font-semibold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600">
                        Latest Drops
                      </h3>
                    </div>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Badge className="px-2 py-0.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600 border-0 shadow-sm">
                        Just arrived
                      </Badge>
                    </motion.div>
                  </div>
                  <p className="text-sm text-gray-600 font-normal">
                    Fresh styles added to our collection
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-5">
                  {megaMenuData.shop.featured.slice(0, 2).map((product) => (
                    <NavigationMenuLink key={product.id} asChild>
                      <Link
                        href={product.href}
                        className="group flex flex-col rounded-xl overflow-hidden border border-gray-100 hover:border-gray-200 hover:shadow-lg transition-all duration-300"
                      >
                        <div className="relative w-full aspect-[4/3] overflow-hidden bg-gray-50">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                          {product.isNew && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3 }}
                              className="absolute top-2 right-2"
                            >
                              <Badge className="px-2 py-0.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-sm">
                                <span className="flex items-center gap-1">
                                  <Sparkles className="h-3 w-3" />
                                  New
                                </span>
                              </Badge>
                            </motion.div>
                          )}
                          <div className="absolute bottom-2 left-2 right-2 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-y-2 group-hover:translate-y-0">
                            <Badge className="bg-white/90 text-gray-800 hover:bg-white border-0 backdrop-blur-sm text-[10px]">
                              {product.colors.length} colors
                            </Badge>
                            <Badge className="bg-white/90 text-green-600 hover:bg-white border-0 backdrop-blur-sm text-[10px]">
                              Quick view
                            </Badge>
                          </div>
                        </div>
                        <div className="p-4">
                          <h4 className="font-medium text-gray-900 group-hover:text-green-600 transition-colors mb-1 line-clamp-1">
                            {product.name}
                          </h4>
                          <p className="text-xs text-gray-600 mb-2 line-clamp-1">
                            {product.description}
                          </p>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-1.5">
                              <span className="text-sm font-bold text-gray-900">
                                ${product.price}
                              </span>
                              {product.originalPrice && (
                                <span className="text-xs text-gray-500 line-through">
                                  ${product.originalPrice}
                                </span>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-3 w-3 ${
                                      i < Math.floor(product.rating)
                                        ? "fill-amber-400 text-amber-400"
                                        : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-xs text-gray-500 ml-1">
                                ({product.reviews})
                              </span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    </NavigationMenuLink>
                  ))}
                </div>
              </div>

              <div className="px-6 py-4 bg-gray-900/95 border-t border-gray-800">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    {megaMenuData.shop.trending.slice(0, 2).map((trend, i) => {
                      const TrendIcon = trend.icon;
                      return (
                        <div
                          key={i}
                          className="flex items-center gap-1 text-xs"
                        >
                          <TrendIcon className="h-3 w-3 text-green-400" />
                          <span className="text-gray-300">{trend.name}</span>
                          <span className="text-green-400 font-medium">
                            {trend.trend}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                  <NavigationMenuLink asChild>
                    <Link
                      href="/new-arrivals"
                      className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white text-xs font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-colors shadow-sm hover:shadow-md"
                    >
                      View All New Arrivals
                      <ArrowRight className="h-3 w-3" />
                    </Link>
                  </NavigationMenuLink>
                </div>
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* Sale */}
        <NavigationMenuItem>
          <NavigationMenuTrigger className="text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100/80 data-[state=open]:bg-red-100/80 rounded-lg">
            <span className="flex items-center gap-1.5">
              <Flame className="h-3.5 w-3.5" />
              Sale
            </span>
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="w-[500px] overflow-hidden rounded-2xl shadow-xl border border-gray-100">
              {/* Hero banner for sale - Enhanced */}
              <div className="relative h-48 bg-gradient-to-r from-red-600 via-rose-600 to-pink-600">
                <div className="absolute inset-0 opacity-30 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjAzKSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==')]"></div>
                <div className="absolute inset-0 opacity-40 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.25),transparent),radial-gradient(circle_at_70%_65%,rgba(255,255,255,0.2),transparent)]"></div>
                <div className="relative h-full p-6 flex flex-col justify-center z-10">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Badge className="w-fit mb-3 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 border-white/30 shadow-md">
                      <span className="flex items-center gap-1.5">
                        <Flame className="h-3.5 w-3.5 animate-pulse" />
                        Limited Time Offer
                      </span>
                    </Badge>
                  </motion.div>
                  <h3 className="text-3xl font-bold text-white mb-2 drop-shadow-md">
                    {megaMenuData.sale.flashSale.title}
                  </h3>
                  <div className="flex items-center gap-2 mb-2">
                    <p className="text-xl font-bold text-white/95 bg-red-500/30 px-2 py-0.5 rounded-md backdrop-blur-sm">
                      {megaMenuData.sale.flashSale.discount}
                    </p>
                    <div className="text-sm text-white/90 bg-white/10 px-2 py-0.5 rounded-md backdrop-blur-sm">
                      {megaMenuData.sale.flashSale.subtitle}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mt-3">
                    <div className="flex -space-x-1.5">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="w-6 h-6 rounded-full border-2 border-white overflow-hidden"
                        >
                          <div
                            className={`w-full h-full bg-gradient-to-r ${
                              i === 0
                                ? "from-red-500 to-rose-500"
                                : i === 1
                                ? "from-pink-500 to-fuchsia-500"
                                : "from-orange-500 to-amber-500"
                            }`}
                          ></div>
                        </div>
                      ))}
                    </div>
                    <span className="text-xs text-white/90">+8 more deals</span>
                  </div>
                </div>
              </div>

              {/* Sale categories - Enhanced */}
              <div className="p-5 bg-gradient-to-b from-gray-50/80 to-white">
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl border border-red-100 hover:shadow-md transition-all duration-300 hover:border-red-200 group">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="p-1.5 rounded-md bg-red-100 text-red-600 group-hover:scale-110 transition-transform duration-300">
                        <Zap className="h-4 w-4" />
                      </div>
                      <span className="font-semibold text-red-600">
                        Flash Sale
                      </span>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1 text-sm">
                      Up to 50% Off
                    </h4>
                    <p className="text-xs text-gray-600 mb-3 line-clamp-1">
                      Ends in 2h 34m
                    </p>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/sale/flash"
                        className="inline-flex items-center gap-1 text-xs font-medium text-red-600 hover:text-red-700 group-hover:underline"
                      >
                        Shop Flash Sale
                        <ArrowRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform duration-300" />
                      </Link>
                    </NavigationMenuLink>
                  </div>

                  <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border border-amber-100 hover:shadow-md transition-all duration-300 hover:border-amber-200 group">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="p-1.5 rounded-md bg-amber-100 text-amber-600 group-hover:scale-110 transition-transform duration-300">
                        <Percent className="h-4 w-4" />
                      </div>
                      <span className="font-semibold text-amber-600">
                        Clearance
                      </span>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1 text-sm">
                      {megaMenuData.sale.seasonal.discount}
                    </h4>
                    <p className="text-xs text-gray-600 mb-3 line-clamp-1">
                      End of season sale
                    </p>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/sale/clearance"
                        className="inline-flex items-center gap-1 text-xs font-medium text-amber-600 hover:text-amber-700 group-hover:underline"
                      >
                        Browse Clearance
                        <ArrowRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform duration-300" />
                      </Link>
                    </NavigationMenuLink>
                  </div>
                </div>

                <div className="mt-5 pt-4 border-t border-dashed border-gray-200">
                  <NavigationMenuLink asChild>
                    <Link
                      href="/sale"
                      className="flex items-center justify-center gap-2 w-full py-2.5 px-4 bg-gradient-to-r from-red-600 to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-700 font-medium shadow-sm hover:shadow-md transform hover:-translate-y-0.5 transition-all duration-300 relative overflow-hidden group"
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        View All Sales
                        <ShoppingBag className="h-4 w-4" />
                      </span>
                      <span className="absolute inset-0 bg-gradient-to-r from-pink-600 to-rose-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    </Link>
                  </NavigationMenuLink>
                </div>
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        {/* About */}
        <NavigationMenuItem>
          <Link href="/about" legacyBehavior passHref>
            <NavigationMenuLink
              className={cn(
                navigationMenuTriggerStyle(),
                "text-sm font-medium bg-transparent hover:bg-gray-100/80 rounded-lg"
              )}
            >
              <span className="flex items-center gap-1.5">
                <Users className="h-3.5 w-3.5 text-indigo-600" />
                About Us
              </span>
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}
