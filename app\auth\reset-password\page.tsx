"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { AuthFormLayout } from "@/components/auth/auth-form-layout";
import { AuthInput } from "@/components/auth/auth-input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { useAuth } from "@/lib/auth-context";

export default function ResetPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [token, setToken] = useState("");
  const { resetPassword } = useAuth();
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    // Extract token from URL
    const tokenParam = searchParams.get("token");
    if (tokenParam) {
      setToken(tokenParam);
    } else {
      setError(
        "Invalid or missing reset token. Please request a new password reset link."
      );
    }
  }, [searchParams]);

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validate passwords match
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    // Validate password strength
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    if (!passwordRegex.test(password)) {
      setError("Password doesn't meet requirements");
      setIsLoading(false);
      return;
    }

    try {
      if (!token) {
        setError("Missing reset token");
        return;
      }

      const success = await resetPassword(token, password);

      if (success) {
        setIsSubmitted(true);
      } else {
        setError("Failed to reset password. The link may have expired.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again later.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AuthFormLayout
      imageUrl="https://assets.lummi.ai/assets/QmajeMPWFjh8Uv5GXZfCMSF8JgJUnLudBNc5QitXCf8UzR?auto=format&w=1500"
      testimonialText="Setting my new password was so straightforward. I was back into my account within minutes!"
      testimonialAuthor="Jamie Williams"
      testimonialRole="Marketing Director at GlobalBrands"
    >
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Set new password</h1>
        <p className="mt-2 text-sm text-muted-foreground">
          Create a new password for your account
        </p>
      </div>
      {isSubmitted ? (
        <div className="space-y-6">
          <Alert className="border-green-500/50 bg-green-500/10 text-green-700">
            <CheckCircle2 className="h-4 w-4" />
            <AlertDescription>
              Your password has been reset successfully. You can now sign in
              with your new password.
            </AlertDescription>
          </Alert>{" "}
          <div className="text-center">
            <Link href="/auth/sign-in">
              <Button className="w-full h-12 text-base font-medium rounded-xl">
                Sign in
              </Button>
            </Link>
          </div>
        </div>
      ) : error && !token ? (
        <div className="space-y-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>{" "}
          <div className="text-center">
            <Link href="/auth/forgot-password">
              <Button className="w-full h-11 text-base font-medium rounded-xl shadow-sm hover:shadow-md transition-all">
                Request New Reset Link
              </Button>
            </Link>
          </div>
        </div>
      ) : (
        <>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-5">
            {" "}
            <div className="space-y-2">
              <Label htmlFor="password">New Password</Label>
              <AuthInput
                id="password"
                type="password"
                autoComplete="new-password"
                required
                value={password}
                error={
                  password &&
                  !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password)
                    ? "Password doesn't meet requirements"
                    : undefined
                }
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setPassword(e.target.value)
                }
              />
              <p className="text-xs text-muted-foreground ml-1 mt-1">
                Password must be at least 8 characters long and include at least
                one uppercase letter, one lowercase letter, and one number.
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <AuthInput
                id="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                value={confirmPassword}
                error={
                  confirmPassword && password !== confirmPassword
                    ? "Passwords do not match"
                    : undefined
                }
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setConfirmPassword(e.target.value)
                }
              />
            </div>{" "}
            <Button
              type="submit"
              className="w-full h-11 text-base font-medium rounded-xl shadow-sm hover:shadow-md transition-all"
              disabled={isLoading}
            >
              {isLoading ? "Setting new password..." : "Reset password"}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm">
            Remember your password?{" "}
            <Link
              href="/auth/sign-in"
              className="font-medium text-primary hover:underline"
            >
              Sign in
            </Link>
          </div>
        </>
      )}
    </AuthFormLayout>
  );
}
