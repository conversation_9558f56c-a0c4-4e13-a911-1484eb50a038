"use client";

import React, { useState, useEffect } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  MessageCircle,
  Search,
  ShoppingBag,
  Truck,
  RotateCcw,
  CreditCard,
  User,
  Package,
  Globe,
  Bookmark,
  Star,
  HelpCircle,
  Phone,
  Mail,
  Clock,
  Calendar,
  Percent,
  Gift,
  Shield,
  AlertCircle,
  CheckCircle,
  X,
  Edit,
} from "lucide-react";

interface FAQTabProps {
  onAskQuestion: (question: string) => void;
}

// FAQ data with expanded categories and questions
const faqs = [
  // Shipping & Delivery
  {
    id: "shipping-options",
    icon: Truck,
    iconColor: "text-blue-500",
    question: "What are your shipping options?",
    answer:
      "We offer standard shipping (3-5 business days) and express shipping (1-2 business days). Free shipping is available on orders over $50.",
    category: "Shipping & Delivery",
    tags: ["shipping", "delivery", "options"],
    popular: true,
  },
  {
    id: "shipping-time",
    icon: Clock,
    iconColor: "text-blue-500",
    question: "How long will it take to receive my order?",
    answer:
      "Standard shipping takes 3-5 business days, while express shipping takes 1-2 business days. International orders may take 7-14 business days depending on the destination country.",
    category: "Shipping & Delivery",
    tags: ["shipping", "delivery", "time", "international"],
    popular: false,
  },
  {
    id: "shipping-tracking",
    icon: Package,
    iconColor: "text-blue-500",
    question: "How can I track my shipment?",
    answer:
      "Once your order ships, you'll receive a confirmation email with tracking information. You can also view your order status and tracking details in your account dashboard under 'Order History'.",
    category: "Shipping & Delivery",
    tags: ["shipping", "tracking", "order status"],
    popular: true,
  },

  // Returns & Refunds
  {
    id: "returns",
    icon: RotateCcw,
    iconColor: "text-amber-500",
    question: "What is your return policy?",
    answer:
      "We accept returns within 30 days of purchase. Items must be unused and in original packaging. Refunds are processed within 5-7 business days after we receive the returned item.",
    category: "Returns & Refunds",
    tags: ["returns", "policy", "refunds"],
    popular: true,
  },
  {
    id: "return-process",
    icon: RotateCcw,
    iconColor: "text-amber-500",
    question: "How do I return an item?",
    answer:
      "To return an item, go to your account dashboard, find the order, and select 'Return Items'. Follow the instructions to print a return label. Package the items in their original packaging and attach the return label. Drop off the package at any authorized shipping location.",
    category: "Returns & Refunds",
    tags: ["returns", "process", "how to"],
    popular: true,
  },
  {
    id: "refund-time",
    icon: Clock,
    iconColor: "text-amber-500",
    question: "When will I receive my refund?",
    answer:
      "Refunds are processed within 5-7 business days after we receive your returned item. Once processed, it may take an additional 3-5 business days for the funds to appear in your account, depending on your payment method and financial institution.",
    category: "Returns & Refunds",
    tags: ["refunds", "timing", "process"],
    popular: false,
  },

  // Payment
  {
    id: "payment-methods",
    icon: CreditCard,
    iconColor: "text-green-500",
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards (Visa, Mastercard, American Express, Discover), PayPal, Apple Pay, Google Pay, and Shop Pay. We also offer financing options through Affirm for orders over $100.",
    category: "Payment",
    tags: ["payment", "credit card", "paypal"],
    popular: true,
  },
  {
    id: "payment-security",
    icon: Shield,
    iconColor: "text-green-500",
    question: "Is my payment information secure?",
    answer:
      "Yes, all payment information is encrypted using industry-standard SSL technology. We never store your full credit card details on our servers. Our payment processing is PCI DSS compliant to ensure the highest level of security.",
    category: "Payment",
    tags: ["payment", "security", "encryption"],
    popular: false,
  },
  {
    id: "payment-issues",
    icon: AlertCircle,
    iconColor: "text-green-500",
    question: "What if my payment is declined?",
    answer:
      "If your payment is declined, first verify that all information was entered correctly. Contact your bank to ensure there are no restrictions on your card. If problems persist, try an alternative payment method or contact our customer support team for assistance.",
    category: "Payment",
    tags: ["payment", "declined", "issues"],
    popular: false,
  },

  // Account
  {
    id: "create-account",
    icon: User,
    iconColor: "text-indigo-500",
    question: "How do I create an account?",
    answer:
      "You can create an account by clicking on the user icon in the top right corner and selecting 'Sign Up'. You'll need to provide your email address and create a password. You can also sign up using your Google, Facebook, or Apple account for faster registration.",
    category: "Account",
    tags: ["account", "registration", "sign up"],
    popular: true,
  },
  {
    id: "account-benefits",
    icon: Star,
    iconColor: "text-indigo-500",
    question: "What are the benefits of creating an account?",
    answer:
      "Creating an account allows you to track orders, save your shipping information for faster checkout, create wishlists, receive personalized recommendations, earn loyalty points, and access exclusive member-only discounts and early access to sales.",
    category: "Account",
    tags: ["account", "benefits", "loyalty"],
    popular: false,
  },
  {
    id: "password-reset",
    icon: Shield,
    iconColor: "text-indigo-500",
    question: "How do I reset my password?",
    answer:
      "To reset your password, click on 'Login' and then select 'Forgot Password'. Enter the email address associated with your account, and we'll send you a password reset link. Follow the instructions in the email to create a new password.",
    category: "Account",
    tags: ["account", "password", "reset"],
    popular: false,
  },

  // Orders
  {
    id: "track-order",
    icon: Package,
    iconColor: "text-purple-500",
    question: "How can I track my order?",
    answer:
      "Once your order ships, you'll receive a confirmation email with tracking information. You can also view your order status in your account dashboard under 'Order History'. Click on any order to see detailed tracking information.",
    category: "Orders",
    tags: ["orders", "tracking", "shipping"],
    popular: true,
  },
  {
    id: "order-cancel",
    icon: X,
    iconColor: "text-purple-500",
    question: "Can I cancel my order?",
    answer:
      "You can cancel your order within 1 hour of placing it if it hasn't entered the processing stage. To cancel, go to your account dashboard, find the order, and click 'Cancel Order'. If the option isn't available, please contact customer support immediately.",
    category: "Orders",
    tags: ["orders", "cancel", "processing"],
    popular: true,
  },
  {
    id: "order-modify",
    icon: Edit,
    iconColor: "text-purple-500",
    question: "Can I modify my order after placing it?",
    answer:
      "You can modify your order within 1 hour of placing it if it hasn't entered the processing stage. This includes changing shipping address, shipping method, or adding/removing items. Please contact customer support immediately for assistance with modifications.",
    category: "Orders",
    tags: ["orders", "modify", "change"],
    popular: false,
  },

  // Products
  {
    id: "cruelty-free",
    icon: ShoppingBag,
    iconColor: "text-pink-500",
    question: "Are your products cruelty-free?",
    answer:
      "Yes, all our products are cruelty-free and we never test on animals. Many of our products are also vegan-friendly, which is indicated on the product page. We're certified by Leaping Bunny and PETA as a cruelty-free brand.",
    category: "Products",
    tags: ["products", "cruelty-free", "vegan"],
    popular: true,
  },
  {
    id: "product-ingredients",
    icon: Shield,
    iconColor: "text-pink-500",
    question: "Where can I find ingredient information?",
    answer:
      "Complete ingredient lists are available on each product page under the 'Ingredients' tab. We're committed to transparency and provide detailed information about the source and purpose of key ingredients. If you have specific allergies, please consult with your healthcare provider before use.",
    category: "Products",
    tags: ["products", "ingredients", "allergies"],
    popular: false,
  },
  {
    id: "product-shelf-life",
    icon: Calendar,
    iconColor: "text-pink-500",
    question: "What is the shelf life of your products?",
    answer:
      "The shelf life varies by product and is indicated on the packaging with a PAO (Period After Opening) symbol. Unopened products typically have a shelf life of 2-3 years. Once opened, most products should be used within 6-12 months for optimal effectiveness.",
    category: "Products",
    tags: ["products", "shelf life", "expiration"],
    popular: false,
  },

  // International
  {
    id: "international-shipping",
    icon: Globe,
    iconColor: "text-teal-500",
    question: "Do you ship internationally?",
    answer:
      "Yes, we ship to over 50 countries worldwide. International shipping rates and delivery times vary by location. You can see specific shipping options and rates during checkout after entering your shipping address.",
    category: "International",
    tags: ["international", "shipping", "global"],
    popular: true,
  },
  {
    id: "customs-duties",
    icon: Package,
    iconColor: "text-teal-500",
    question: "Will I have to pay customs or import duties?",
    answer:
      "International orders may be subject to customs fees, import duties, or taxes imposed by the destination country. These fees are the responsibility of the customer and are not included in our shipping charges. Please check your local customs regulations before ordering.",
    category: "International",
    tags: ["international", "customs", "duties", "taxes"],
    popular: true,
  },
  {
    id: "international-returns",
    icon: RotateCcw,
    iconColor: "text-teal-500",
    question: "How do international returns work?",
    answer:
      "International customers can return items within 30 days of delivery. Unlike domestic returns, you'll need to arrange and pay for return shipping to our warehouse. Once we receive and process your return, we'll issue a refund for the product cost (excluding original shipping fees).",
    category: "International",
    tags: ["international", "returns", "refunds"],
    popular: false,
  },

  // Promotions & Discounts
  {
    id: "current-promotions",
    icon: Percent,
    iconColor: "text-red-500",
    question: "What promotions are currently available?",
    answer:
      "Our current promotions include 15% off your first order with code WELCOME15, free shipping on orders over $50, and our seasonal sale with up to 40% off select items. Visit our Promotions page for all current offers and sign up for our newsletter to receive exclusive deals.",
    category: "Promotions & Discounts",
    tags: ["promotions", "discounts", "sales", "coupons"],
    popular: true,
  },
  {
    id: "loyalty-program",
    icon: Gift,
    iconColor: "text-red-500",
    question: "How does your loyalty program work?",
    answer:
      "Our loyalty program rewards you with points for every purchase. You earn 1 point per $1 spent, and points can be redeemed for discounts on future purchases. You also receive bonus points for reviews, referrals, and on your birthday. Different membership tiers offer additional perks like free shipping and exclusive offers.",
    category: "Promotions & Discounts",
    tags: ["loyalty", "rewards", "points", "program"],
    popular: true,
  },
  {
    id: "coupon-usage",
    icon: Percent,
    iconColor: "text-red-500",
    question: "Can I use multiple coupons on one order?",
    answer:
      "Generally, only one coupon code can be applied per order. However, some promotions (like free shipping over $50) can be combined with coupon codes. Loyalty points redemptions can usually be combined with most other promotions. The checkout system will automatically apply the best available discount.",
    category: "Promotions & Discounts",
    tags: ["coupons", "discounts", "promotions", "codes"],
    popular: false,
  },

  // Contact & Support
  {
    id: "contact-methods",
    icon: Phone,
    iconColor: "text-blue-400",
    question: "How can I contact customer support?",
    answer:
      "You can reach our customer support team via <NAME_EMAIL>, by phone at ************** (Mon-Fri, 9am-6pm EST), through live chat on our website (24/7), or by using the contact form on our Contact Us page. We typically respond to all inquiries within 24 hours.",
    category: "Contact & Support",
    tags: ["contact", "support", "help", "service"],
    popular: true,
  },
  {
    id: "business-hours",
    icon: Clock,
    iconColor: "text-blue-400",
    question: "What are your customer service hours?",
    answer:
      "Our customer service team is available by phone and email Monday through Friday from 9am to 6pm EST. Our live chat support is available 24/7. During holidays, hours may be limited, which will be announced on our website and social media channels.",
    category: "Contact & Support",
    tags: ["hours", "support", "contact", "availability"],
    popular: false,
  },
  {
    id: "feedback",
    icon: MessageCircle,
    iconColor: "text-blue-400",
    question: "How can I provide feedback about my experience?",
    answer:
      "We value your feedback! You can share your experience by leaving a product review, responding to our post-purchase email survey, contacting our customer support team, or engaging with us on social media. For more detailed feedback, please use the feedback form on our Contact Us page.",
    category: "Contact & Support",
    tags: ["feedback", "reviews", "suggestions", "experience"],
    popular: false,
  },
];

const FAQTab: React.FC<FAQTabProps> = ({ onAskQuestion }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"all" | "popular">("all");
  const [relatedQuestions, setRelatedQuestions] = useState<string[]>([]);

  // Get unique categories
  const categories = Array.from(new Set(faqs.map((faq) => faq.category)));

  // Filter FAQs based on search, category, and view mode
  const filteredFaqs = faqs.filter((faq) => {
    // Search filter
    const matchesSearch =
      searchQuery === "" ||
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      );

    // Category filter
    const matchesCategory =
      selectedCategory === null || faq.category === selectedCategory;

    // View mode filter
    const matchesViewMode =
      viewMode === "all" || (viewMode === "popular" && faq.popular);

    return matchesSearch && matchesCategory && matchesViewMode;
  });

  // Handle asking a question
  const handleAskQuestion = (question: string) => {
    // Find related questions based on the current question
    const currentFaq = faqs.find((faq) => faq.question === question);
    if (currentFaq) {
      const relatedFaqs = faqs
        .filter(
          (faq) =>
            faq.id !== currentFaq.id &&
            (faq.category === currentFaq.category ||
              faq.tags.some((tag) => currentFaq.tags.includes(tag)))
        )
        .slice(0, 3)
        .map((faq) => faq.question);

      setRelatedQuestions(relatedFaqs);
    }

    onAskQuestion(question);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setSelectedCategory(null);
    setViewMode("all");
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
          <h3 className="font-medium text-xl">Frequently Asked Questions</h3>
          <Badge variant="outline" className="bg-primary/5 px-2 py-1 w-fit">
            <HelpCircle className="h-3 w-3 mr-1 text-indigo-500" />
            Help Center
          </Badge>
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search for answers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-muted/30 border-muted-foreground/20 focus-visible:ring-indigo-500"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* View Mode Tabs */}
        <Tabs
          value={viewMode}
          onValueChange={(value) => setViewMode(value as "all" | "popular")}
          className="mb-4"
        >
          <TabsList className="grid w-full grid-cols-2 h-9">
            <TabsTrigger value="all" className="text-xs">
              All Questions
            </TabsTrigger>
            <TabsTrigger value="popular" className="text-xs">
              Popular Questions
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Category badges */}
        <div className="flex flex-wrap gap-2 mb-4">
          <Badge
            variant={selectedCategory === null ? "default" : "outline"}
            className={`cursor-pointer transition-colors ${
              selectedCategory === null
                ? "bg-indigo-500 hover:bg-indigo-600"
                : "bg-muted/50 hover:bg-muted"
            }`}
            onClick={() => setSelectedCategory(null)}
          >
            All Categories
          </Badge>

          {categories.map((category) => (
            <Badge
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              className={`cursor-pointer transition-colors ${
                selectedCategory === category
                  ? "bg-indigo-500 hover:bg-indigo-600"
                  : "bg-muted/50 hover:bg-muted"
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Badge>
          ))}
        </div>

        {/* Active Filters */}
        {(searchQuery || selectedCategory || viewMode === "popular") && (
          <div className="flex items-center justify-between mb-4 bg-muted/30 p-2 rounded-md">
            <div className="flex flex-wrap gap-1 text-xs text-muted-foreground">
              <span>Filters:</span>
              {searchQuery && (
                <span className="font-medium">"{searchQuery}"</span>
              )}
              {selectedCategory && (
                <span className="font-medium">{selectedCategory}</span>
              )}
              {viewMode === "popular" && (
                <span className="font-medium">Popular Only</span>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs"
              onClick={clearFilters}
            >
              Clear All
            </Button>
          </div>
        )}

        {/* No Results */}
        {filteredFaqs.length === 0 && (
          <div className="text-center py-8">
            <div className="bg-muted/50 p-4 rounded-full inline-flex items-center justify-center mb-4">
              <Search className="h-6 w-6 text-muted-foreground" />
            </div>
            <h4 className="font-medium text-lg mb-2">No results found</h4>
            <p className="text-sm text-muted-foreground mb-4">
              We couldn't find any FAQs matching your search criteria.
            </p>
            <Button variant="outline" onClick={clearFilters} className="mr-2">
              Clear Filters
            </Button>
            <Button
              onClick={() =>
                handleAskQuestion(`I need help with: ${searchQuery}`)
              }
              className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
            >
              Ask Directly
            </Button>
          </div>
        )}

        {/* FAQ Accordion */}
        {filteredFaqs.length > 0 && (
          <Accordion type="single" collapsible className="w-full">
            {filteredFaqs.map((faq) => (
              <AccordionItem
                key={faq.id}
                value={faq.id}
                className="border-b border-border/50"
              >
                <AccordionTrigger className="text-sm font-medium py-3 hover:no-underline">
                  <div className="flex items-start sm:items-center gap-3 text-left">
                    <div
                      className={`p-2 rounded-full bg-muted ${faq.iconColor}/10 shrink-0`}
                    >
                      <faq.icon className={`h-4 w-4 ${faq.iconColor}`} />
                    </div>
                    <div className="flex flex-col">
                      <span className="text-left">{faq.question}</span>
                      {faq.popular && (
                        <span className="text-xs text-indigo-500 font-normal flex items-center mt-0.5">
                          <Star className="h-3 w-3 mr-1 fill-indigo-500" />
                          Popular question
                        </span>
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-2 pb-4">
                  <div className="pl-10">
                    <p className="text-sm text-muted-foreground mb-3">
                      {faq.answer}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {faq.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="bg-muted/30 text-xs px-1.5 py-0 h-5"
                        >
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <Badge
                        variant="outline"
                        className="bg-muted/30 text-xs w-fit"
                      >
                        {faq.category}
                      </Badge>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                          onClick={() => {
                            navigator.clipboard.writeText(faq.answer);
                          }}
                        >
                          Copy
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                          onClick={() => handleAskQuestion(faq.question)}
                        >
                          <MessageCircle className="mr-2 h-3 w-3 text-indigo-500" />
                          <span className="hidden sm:inline">
                            Ask about this
                          </span>
                          <span className="inline sm:hidden">Ask</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}

        {/* Related Questions */}
        {relatedQuestions.length > 0 && (
          <div className="mt-6 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-3 flex items-center">
              <HelpCircle className="h-4 w-4 mr-2 text-indigo-500" />
              Related Questions
            </h4>
            <div className="space-y-2">
              {relatedQuestions.map((question, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-left h-auto py-2 border-indigo-100"
                  onClick={() => handleAskQuestion(question)}
                >
                  <MessageCircle className="mr-2 h-3 w-3 text-indigo-500 shrink-0" />
                  <span className="truncate">{question}</span>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Contact Us */}
        <div className="mt-8 text-center bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg">
          <p className="text-sm font-medium mb-3">
            Can't find what you're looking for?
          </p>
          <div className="flex flex-col xs:flex-row gap-2 justify-center">
            <Button
              variant="outline"
              onClick={() =>
                handleAskQuestion("I'd like to contact customer support")
              }
              className="border-indigo-200 text-sm h-9"
            >
              <Phone className="mr-2 h-4 w-4 text-indigo-500" />
              Contact Support
            </Button>
            <Button
              onClick={() =>
                handleAskQuestion("I need help with something not in the FAQs")
              }
              className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 text-sm h-9"
            >
              <MessageCircle className="mr-2 h-4 w-4" />
              Start a conversation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQTab;
