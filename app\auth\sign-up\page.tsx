"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { AuthFormLayout } from "@/components/auth/auth-form-layout";
import { AuthInput } from "@/components/auth/auth-input";
import { SocialButtons } from "@/components/auth/social-buttons";
import { useAuth } from "@/lib/auth-context";

export default function SignUpPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const { signUp, signInWithGoogle, signInWithFacebook } = useAuth();
  const router = useRouter();

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validate passwords match
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    // Validate password strength
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    if (!passwordRegex.test(password)) {
      setError("Password doesn't meet requirements");
      setIsLoading(false);
      return;
    }

    try {
      const success = await signUp(email, password, firstName, lastName);

      if (success) {
        router.push("/"); // Redirect to home page on successful sign up
      } else {
        setError("Failed to create account. Please try again.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again later.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AuthFormLayout
      imageUrl="https://assets.lummi.ai/assets/Qmd1KUikNSQzn1E4SV5ghB2zCBuEtZh4ZXhhXf2wtoGLBb?auto=format&w=1500"
      testimonialText="Joining this platform was one of the best business decisions we made this year. The onboarding process was seamless and the user experience is exceptional."
      testimonialAuthor="Michael Chen"
      testimonialRole="Founder at InnovateTech"
    >
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Create an account</h1>
        <p className="mt-2 text-sm text-muted-foreground">
          Enter your information below to create your account
        </p>
      </div>
      <form onSubmit={handleSubmit} className="space-y-5">
        {" "}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}{" "}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First name</Label>
            <AuthInput
              id="firstName"
              placeholder="John"
              required
              value={firstName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setFirstName(e.target.value)
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last name</Label>
            <AuthInput
              id="lastName"
              placeholder="Doe"
              required
              value={lastName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setLastName(e.target.value)
              }
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <AuthInput
            id="email"
            placeholder="<EMAIL>"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setEmail(e.target.value)
            }
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <AuthInput
            id="password"
            type="password"
            autoComplete="new-password"
            required
            value={password}
            error={
              password &&
              !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password)
                ? "Password doesn't meet requirements"
                : undefined
            }
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setPassword(e.target.value)
            }
          />
          <p className="text-xs text-muted-foreground ml-1 mt-1">
            Password must be at least 8 characters long and include at least one
            uppercase letter, one lowercase letter, and one number.
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirm password</Label>
          <AuthInput
            id="confirmPassword"
            type="password"
            autoComplete="new-password"
            required
            value={confirmPassword}
            error={
              confirmPassword && password !== confirmPassword
                ? "Passwords do not match"
                : undefined
            }
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setConfirmPassword(e.target.value)
            }
          />
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id="terms" required />
          <Label
            htmlFor="terms"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            I agree to the{" "}
            <Link href="/terms" className="text-primary hover:underline">
              terms of service
            </Link>{" "}
            and{" "}
            <Link href="/privacy" className="text-primary hover:underline">
              privacy policy
            </Link>
          </Label>
        </div>{" "}
        <Button
          type="submit"
          className="w-full h-11 text-base font-medium rounded-xl shadow-sm hover:shadow-md transition-all"
          disabled={isLoading}
        >
          {isLoading ? "Creating account..." : "Create account"}
        </Button>
      </form>{" "}
      <div className="mt-6 text-center text-sm">
        Already have an account?{" "}
        <Link
          href="/auth/sign-in"
          className="font-medium text-primary hover:underline"
        >
          Sign in
        </Link>
      </div>
      <div className="mt-6">
        <SocialButtons
          isLoading={isLoading}
          onGoogleClick={async () => {
            setIsLoading(true);
            setError("");
            try {
              const success = await signInWithGoogle();
              if (success) {
                router.push("/");
              } else {
                setError("Google sign-up failed. Please try again.");
              }
            } catch (err) {
              setError("An error occurred during Google sign-up.");
              console.error(err);
            } finally {
              setIsLoading(false);
            }
          }}
          onFacebookClick={async () => {
            setIsLoading(true);
            setError("");
            try {
              const success = await signInWithFacebook();
              if (success) {
                router.push("/");
              } else {
                setError("Facebook sign-up failed. Please try again.");
              }
            } catch (err) {
              setError("An error occurred during Facebook sign-up.");
              console.error(err);
            } finally {
              setIsLoading(false);
            }
          }}
        />
      </div>
    </AuthFormLayout>
  );
}
