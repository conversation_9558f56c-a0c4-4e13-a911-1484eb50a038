import React from "react";

export default function Logo() {
  return (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    width="44.8917mm"
    height="5.066mm"
    viewBox="0 0 127.2522 14.3604"
 
  >
    <defs>
      <style>{".cls-1{fill:#231f20}"}</style>
    </defs>
    <g id="Layer_2" data-name="Layer 2">
      <g id="Art">
        <path
          className="cls-1"
          d="M3.54 6.184c0 .56.063.56.685.56h1.412A7.816 7.816 0 007.4 6.62c.228-.083.394-.186.519-.726l.186-.83a.436.436 0 01.54 0c0 .498-.083 1.328-.083 2.096 0 .747.083 1.535.083 2.013-.083.124-.394.124-.54 0l-.207-.81a.964.964 0 00-.664-.788 7.051 7.051 0 00-1.598-.104H4.225c-.622 0-.684.02-.684.56v2.884c0 2.18.041 2.49 1.265 2.594l.706.063c.124.083.083.456-.042.518a78.143 78.143 0 00-2.76-.062c-.913 0-1.618.021-2.593.062a.403.403 0 01-.042-.518l.519-.063c1.203-.145 1.245-.415 1.245-2.594v-7.47C1.839 1.266 1.798.975.594.83L.262.789C.137.706.179.332.303.27c.872.041 1.578.062 2.408.062h3.818c1.328 0 2.532 0 2.802-.062 0 .954.02 1.95.062 2.74a.374.374 0 01-.498.061C8.729 2.137 8.48 1.39 7.442 1.1A10.253 10.253 0 005.43.975H4.163c-.622 0-.622.042-.622.83zM15.448 7.326A7.173 7.173 0 0122.836 0a6.83 6.83 0 017.2 7.118 7.085 7.085 0 01-7.2 7.242 6.988 6.988 0 01-7.388-7.034zm12.514.435c0-3.424-1.515-7.118-5.479-7.118-2.158 0-4.96 1.474-4.96 6.019 0 3.07 1.494 7.055 5.582 7.055 2.49 0 4.857-1.868 4.857-5.956zM37.792 3.465c0-1.97-.063-2.324-.913-2.427l-.664-.083a.346.346 0 01.02-.519C37.397.332 38.83.27 40.863.27a7.352 7.352 0 013.466.622 3.093 3.093 0 011.722 2.905 3.838 3.838 0 01-2.594 3.466c-.104.125 0 .332.104.498 1.66 2.677 2.76 4.337 4.171 5.52a2.483 2.483 0 001.432.581.174.174 0 01.02.29 3.22 3.22 0 01-.913.104c-1.764 0-2.822-.519-4.296-2.614-.54-.769-1.39-2.2-2.033-3.134a1.482 1.482 0 00-1.474-.664c-.934 0-.975.02-.975.456v2.615c0 2.179.041 2.428 1.245 2.594l.436.062a.405.405 0 01-.042.519 52.931 52.931 0 00-2.47-.062c-.87 0-1.618.02-2.593.062a.4.4 0 01-.041-.519l.518-.062c1.204-.145 1.245-.415 1.245-2.594zm1.701 2.968a1.64 1.64 0 00.062.685c.063.062.374.104 1.432.104a3.475 3.475 0 002.138-.54 2.975 2.975 0 001.038-2.574 3.057 3.057 0 00-3.32-3.195c-1.287 0-1.35.083-1.35.664zM55.322 3.445c0-2.158-.042-2.47-1.266-2.615L53.724.79c-.125-.083-.083-.457.041-.519.893.041 1.598.062 2.45.062h3.901A27.961 27.961 0 0062.917.27a23.326 23.326 0 01.332 2.76.409.409 0 01-.519.042c-.31-.976-.498-1.702-1.577-1.972A9.877 9.877 0 0059.14.975h-1.494c-.622 0-.622.042-.622.83v4.15c0 .581.062.581.685.581h1.203a7.708 7.708 0 001.764-.124c.249-.083.394-.208.498-.727l.166-.85a.41.41 0 01.54.02c0 .498-.083 1.308-.083 2.096 0 .747.083 1.536.083 1.992a.41.41 0 01-.54.021l-.187-.809a.887.887 0 00-.643-.789 6.947 6.947 0 00-1.598-.103h-1.203c-.623 0-.685.02-.685.56v2.927c0 1.1.062 1.806.394 2.158.249.25.685.478 2.51.478a6.067 6.067 0 002.657-.312 5.204 5.204 0 001.473-2.012.381.381 0 01.52.145 18.031 18.031 0 01-.935 2.884 252.9 252.9 0 00-5.561-.062h-1.867c-.893 0-1.599.02-2.823.062a.403.403 0 01-.042-.519l.685-.062c1.183-.104 1.287-.415 1.287-2.594zM71.006 2.532C70.528 1.37 70.217.934 69.325.83L68.95.789a.348.348 0 01.042-.519c.602.041 1.266.062 2.158.062.913 0 1.64-.021 2.51-.062a.363.363 0 01.042.519l-.31.041c-.79.104-.956.228-.976.394a26.031 26.031 0 001.037 2.905c.955 2.408 1.91 4.794 2.947 7.16.644-1.37 1.536-3.507 1.992-4.607a166.753 166.753 0 001.93-4.648 2.208 2.208 0 00.187-.81c0-.145-.207-.311-.934-.394l-.373-.042a.363.363 0 01.04-.518c.582.041 1.37.062 2.16.062.684 0 1.327-.021 2.012-.062a.409.409 0 01.042.519l-.623.041a1.326 1.326 0 00-1.079.726 50.132 50.132 0 00-2.324 4.774L78.31 8.882c-.83 1.909-1.806 4.316-2.158 5.416a.423.423 0 01-.229.062.842.842 0 01-.31-.062 20.666 20.666 0 00-.955-2.76zM89.76 3.445c0-2.158-.041-2.47-1.265-2.615L88.163.79c-.125-.083-.083-.457.04-.519.893.041 1.599.062 2.45.062h3.901A27.962 27.962 0 0097.356.27a23.31 23.31 0 01.332 2.76.408.408 0 01-.52.042c-.31-.976-.497-1.702-1.577-1.972A9.877 9.877 0 0093.58.975h-1.494c-.623 0-.623.042-.623.83v4.15c0 .581.062.581.685.581h1.203a7.708 7.708 0 001.764-.124c.25-.083.394-.208.498-.726l.166-.85a.41.41 0 01.54.02c0 .498-.083 1.307-.083 2.095 0 .747.083 1.537.083 1.993a.41.41 0 01-.54.02l-.187-.808a.887.887 0 00-.643-.79 6.949 6.949 0 00-1.598-.103h-1.203c-.623 0-.685.02-.685.56v2.927c0 1.1.062 1.806.394 2.158.25.25.685.478 2.511.478a6.067 6.067 0 002.657-.312 5.205 5.205 0 001.473-2.012.382.382 0 01.519.145 18.041 18.041 0 01-.934 2.884 252.903 252.903 0 00-5.562-.062h-1.867c-.893 0-1.598.02-2.822.062a.403.403 0 01-.042-.519l.685-.062c1.183-.104 1.286-.415 1.286-2.594zM106.025 3.465c0-1.97-.062-2.324-.913-2.427l-.664-.083a.346.346 0 01.02-.519c1.163-.104 2.595-.166 4.628-.166a7.352 7.352 0 013.466.622 3.093 3.093 0 011.722 2.905 3.838 3.838 0 01-2.594 3.466c-.103.125 0 .332.104.498 1.66 2.677 2.76 4.337 4.171 5.52a2.483 2.483 0 001.432.581.174.174 0 01.02.29 3.22 3.22 0 01-.913.104c-1.764 0-2.822-.519-4.296-2.614-.539-.769-1.39-2.2-2.033-3.134a1.482 1.482 0 00-1.474-.664c-.933 0-.975.02-.975.456v2.615c0 2.179.042 2.428 1.245 2.594l.436.062a.405.405 0 01-.042.519 52.931 52.931 0 00-2.469-.062c-.872 0-1.619.02-2.594.062a.4.4 0 01-.041-.519l.518-.062c1.204-.145 1.245-.415 1.245-2.594zm1.702 2.968a1.64 1.64 0 00.062.685c.062.062.374.104 1.432.104a3.475 3.475 0 002.137-.54 2.975 2.975 0 001.038-2.574 3.057 3.057 0 00-3.32-3.195c-1.287 0-1.349.083-1.349.664zM121.18.88h-1.458v4.037h-.77V.882h-1.458V.259h3.686zm6.072 4.037h-.77v-3.89h-.022l-1.526 3.89h-.486l-1.527-3.89h-.022v3.89h-.769V.26h1.187l1.391 3.539 1.368-3.54h1.176z"
        />
      </g>
    </g>
  </svg>
  );
}
