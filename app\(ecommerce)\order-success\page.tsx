"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  CheckCircle,
  Package,
  Truck,
  Mail,
  Download,
  Share2,
  ArrowRight,
  Calendar,
  MapPin,
  CreditCard,
  Star,
  Gift,
  ShoppingBag,
  Home,
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/lib/auth-context";

// Mock order data - in real app this would come from API based on order ID
const mockOrderData = {
  id: "ORD-2024-001235",
  status: "confirmed",
  orderDate: new Date().toISOString(),
  estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  total: 156.99,
  subtotal: 139.99,
  shipping: 9.99,
  tax: 7.01,
  paymentMethod: "•••• •••• •••• 4242",
  items: [
    {
      id: "1",
      name: "<PERSON><PERSON><PERSON>low Serum",
      image: "https://images.unsplash.com/photo-1620916566398-39f1143ab7be?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      price: 89.99,
      quantity: 1,
      size: "30ml",
    },
    {
      id: "2",
      name: "Luxury Face Cream",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      price: 49.99,
      quantity: 1,
      size: "50ml",
    },
  ],
  shippingAddress: {
    name: "Sarah Johnson",
    street: "123 Beauty Lane",
    city: "Los Angeles",
    state: "CA",
    zip: "90210",
    country: "United States",
  },
};

export default function OrderSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [order] = useState(mockOrderData);
  const [isSharing, setIsSharing] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const handleShare = async () => {
    setIsSharing(true);
    try {
      if (navigator.share) {
        await navigator.share({
          title: "My Beauty Order",
          text: `I just placed an order for amazing beauty products! Order #${order.id}`,
          url: window.location.href,
        });
      } else {
        // Fallback to copying to clipboard
        await navigator.clipboard.writeText(window.location.href);
        alert("Order link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
    } finally {
      setIsSharing(false);
    }
  };

  const downloadInvoice = () => {
    // In real app, this would download the actual invoice
    alert("Invoice download would start here");
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Order Confirmed!</h1>
          <p className="text-lg text-gray-600 mb-4">
            Thank you for your purchase. Your order has been successfully placed.
          </p>
          <div className="flex items-center justify-center gap-2">
            <span className="text-gray-600">Order Number:</span>
            <Badge variant="secondary" className="font-mono">
              {order.id}
            </Badge>
          </div>
        </motion.div>

        {/* Order Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm p-6 mb-6"
        >
          <h2 className="text-xl font-semibold mb-6">What's Next?</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Order Confirmed</h3>
                <p className="text-sm text-gray-600">
                  We've received your order and payment
                </p>
              </div>
              <span className="text-sm text-gray-500">Just now</span>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Package className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Processing</h3>
                <p className="text-sm text-gray-600">
                  We're preparing your items for shipment
                </p>
              </div>
              <span className="text-sm text-gray-500">1-2 business days</span>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <Truck className="h-4 w-4 text-gray-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-500">Shipped</h3>
                <p className="text-sm text-gray-500">
                  Your order is on its way to you
                </p>
              </div>
              <span className="text-sm text-gray-500">3-5 business days</span>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-gray-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-500">Delivered</h3>
                <p className="text-sm text-gray-500">
                  Estimated delivery: {formatDate(order.estimatedDelivery)}
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Order Details */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <h3 className="text-lg font-semibold mb-4">Order Details</h3>
            
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item.id} className="flex gap-3">
                  <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.name}</h4>
                    <p className="text-sm text-gray-600">Size: {item.size}</p>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-sm text-gray-600">Qty: {item.quantity}</span>
                      <span className="font-medium">${item.price}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <Separator className="my-4" />

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span>${order.subtotal}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Shipping</span>
                <span>${order.shipping}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tax</span>
                <span>${order.tax}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Total</span>
                <span>${order.total}</span>
              </div>
            </div>
          </motion.div>

          {/* Shipping & Payment Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-6"
          >
            {/* Shipping Address */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Shipping Address
              </h3>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="font-medium text-gray-900">{order.shippingAddress.name}</div>
                <div>{order.shippingAddress.street}</div>
                <div>
                  {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zip}
                </div>
                <div>{order.shippingAddress.country}</div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Method
              </h3>
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{order.paymentMethod}</span>
              </div>
            </div>

            {/* Confirmation Email */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Mail className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">Confirmation Email Sent</span>
              </div>
              <p className="text-sm text-blue-700">
                We've sent a confirmation email with your order details and tracking information.
              </p>
            </div>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8 flex flex-col sm:flex-row gap-4 justify-center"
        >
          <Button onClick={downloadInvoice} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download Invoice
          </Button>
          
          <Button onClick={handleShare} variant="outline" disabled={isSharing}>
            <Share2 className="h-4 w-4 mr-2" />
            {isSharing ? "Sharing..." : "Share Order"}
          </Button>
          
          {user && (
            <Link href="/my-orders">
              <Button variant="outline">
                <Package className="h-4 w-4 mr-2" />
                View All Orders
              </Button>
            </Link>
          )}
          
          <Link href="/products">
            <Button>
              <ShoppingBag className="h-4 w-4 mr-2" />
              Continue Shopping
            </Button>
          </Link>
        </motion.div>

        {/* Recommendations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-12 bg-white rounded-lg shadow-sm p-6"
        >
          <h3 className="text-lg font-semibold mb-4">You Might Also Like</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                id: "rec-1",
                name: "Vitamin C Serum",
                price: 79.99,
                image: "https://images.unsplash.com/photo-1620916566398-39f1143ab7be?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
              },
              {
                id: "rec-2",
                name: "Hydrating Moisturizer",
                price: 59.99,
                image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
              },
              {
                id: "rec-3",
                name: "Anti-Aging Cream",
                price: 129.99,
                image: "https://images.unsplash.com/photo-1596755389378-c31d21fd1273?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
              },
            ].map((product) => (
              <div key={product.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="w-full h-32 bg-gray-100 rounded-lg overflow-hidden mb-3">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h4 className="font-medium text-gray-900 mb-1">{product.name}</h4>
                <div className="flex items-center justify-between">
                  <span className="font-semibold">${product.price}</span>
                  <Button size="sm">Add to Cart</Button>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Back to Home */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-8 text-center"
        >
          <Link href="/" className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900">
            <Home className="h-4 w-4" />
            Back to Home
          </Link>
        </motion.div>
      </div>
    </div>
  );
}
