export interface Product {
  id: number;
  name: string;
  price: number;
  image: string;
  description: string;
  rating: number;
  slug: string;
  subImages: string[];
  category: string;
  categorySlug: string;
  brand?: string;
  colors?: string[];
  sizes?: string[];
  inStock: boolean;
  isNew?: boolean;
  isOnSale?: boolean;
  originalPrice?: number;
}

export const product: Product[] = [
  {
    id: 1,
    name: "Forever Aloe Cooling Lotion",
    price: 86,
    image: "/564.webp",
    description:
      "Forever Aloe Cooling Lotion is designed to provide muscle and joint support, offering a soothing and cooling sensation. Infused with a blend of aloe, eucalyptus, and menthol, this lotion helps to alleviate discomfort and promote relaxation. Perfect for post-exercise recovery or daily relief, its formula penetrates the skin, delivering the benefits of natural ingredients directly to where they are needed most.",
    rating: 4.5,
    slug: "forever-aloe-cooling-lotion",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Training Shoes",
    categorySlug: "training-shoes",
    brand: "Forever",
    colors: ["Blue", "Green"],
    sizes: ["S", "M", "L"],
    inStock: true,
    isNew: true
  },
  {
    id: 2,
    name: "Forever Nourishing Hair Oil",
    price: 100,
    image: "/642.webp",
    description:
      "Forever Nourishing Hair Oil provides deep nourishment for healthy, lustrous hair. Enriched with natural oils and vitamins, this premium formula strengthens hair from root to tip while adding shine and manageability.",
    rating: 4,
    slug: "forever-nourishing-hair-oil",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Lifestyle",
    categorySlug: "lifestyle",
    brand: "Forever",
    colors: ["Natural"],
    sizes: ["100ml", "200ml"],
    inStock: true,
    isOnSale: true,
    originalPrice: 120
  },
  {
    id: 3,
    name: "Air Max Pro Running Shoes",
    price: 150,
    image: "/656.png",
    description:
      "Experience ultimate performance with our Air Max Pro Running Shoes. Featuring advanced cushioning technology and breathable mesh upper for maximum comfort during your runs.",
    rating: 4.8,
    slug: "air-max-pro-running-shoes",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Running Shoes",
    categorySlug: "running-shoes",
    brand: "AirMax",
    colors: ["Black", "White", "Blue"],
    sizes: ["7", "8", "9", "10", "11", "12"],
    inStock: true,
    isNew: true
  },
  {
    id: 4,
    name: "Street Style Casual Sneakers",
    price: 89,
    image: "/651.webp",
    description:
      "Perfect blend of comfort and style for everyday wear. These casual sneakers feature premium leather construction and modern design elements.",
    rating: 4.3,
    slug: "street-style-casual-sneakers",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Casual Sneakers",
    categorySlug: "casual-sneakers",
    brand: "StreetWear",
    colors: ["White", "Black", "Gray"],
    sizes: ["7", "8", "9", "10", "11"],
    inStock: true
  },
  {
    id: 5,
    name: "Court Master Basketball Shoes",
    price: 180,
    image: "/822.png",
    description:
      "Dominate the court with these high-performance basketball shoes. Enhanced ankle support and superior grip for explosive movements.",
    rating: 4.7,
    slug: "court-master-basketball-shoes",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Basketball Shoes",
    categorySlug: "basketball-shoes",
    brand: "CourtMaster",
    colors: ["Red", "Black", "White"],
    sizes: ["8", "9", "10", "11", "12", "13"],
    inStock: true,
    isOnSale: true,
    originalPrice: 220
  },
  {
    id: 6,
    name: "CrossFit Training Shoes",
    price: 120,
    image: "/564.webp",
    description:
      "Versatile training shoes designed for intense workouts. Stable platform and flexible design for all your fitness activities.",
    rating: 4.4,
    slug: "crossfit-training-shoes",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Training Shoes",
    categorySlug: "training-shoes",
    brand: "FitPro",
    colors: ["Black", "Blue", "Green"],
    sizes: ["7", "8", "9", "10", "11", "12"],
    inStock: true
  },
  {
    id: 7,
    name: "Elite Running Sneakers",
    price: 175,
    image: "/656.png",
    description:
      "Professional-grade running shoes with advanced cushioning technology. Perfect for marathon training and competitive running.",
    rating: 4.9,
    slug: "elite-running-sneakers",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Running Shoes",
    categorySlug: "running-shoes",
    brand: "RunElite",
    colors: ["Black", "White", "Red"],
    sizes: ["7", "8", "9", "10", "11", "12"],
    inStock: true,
    isNew: true
  },
  {
    id: 8,
    name: "Urban Casual Sneakers",
    price: 95,
    image: "/651.webp",
    description:
      "Modern urban design meets comfort. Perfect for city walks and casual outings with friends.",
    rating: 4.2,
    slug: "urban-casual-sneakers",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Casual Sneakers",
    categorySlug: "casual-sneakers",
    brand: "UrbanStyle",
    colors: ["White", "Gray", "Navy"],
    sizes: ["6", "7", "8", "9", "10", "11"],
    inStock: true
  },
  {
    id: 9,
    name: "Pro Basketball High-Tops",
    price: 200,
    image: "/822.png",
    description:
      "Professional basketball shoes with maximum ankle support and court grip. Designed for serious players.",
    rating: 4.8,
    slug: "pro-basketball-high-tops",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Basketball Shoes",
    categorySlug: "basketball-shoes",
    brand: "ProCourt",
    colors: ["Black", "White", "Red"],
    sizes: ["8", "9", "10", "11", "12", "13", "14"],
    inStock: true,
    isOnSale: true,
    originalPrice: 250
  },
  {
    id: 10,
    name: "Lightweight Training Shoes",
    price: 110,
    image: "/564.webp",
    description:
      "Ultra-lightweight design for agility training and HIIT workouts. Breathable mesh upper for maximum comfort.",
    rating: 4.3,
    slug: "lightweight-training-shoes",
    subImages: [
      "/cooling-gel-2023-05-12-645e04341a321.webp",
      "/cooling-gel-2023-05-12-645e043747dfe.webp",
      "/cooling-gel-2023-05-12-645e044525f7b.webp"
    ],
    category: "Training Shoes",
    categorySlug: "training-shoes",
    brand: "AgileFit",
    colors: ["Gray", "Blue", "Black"],
    sizes: ["7", "8", "9", "10", "11"],
    inStock: true
  }
];

// Helper function to get products by category
export const getProductsByCategory = (categorySlug: string): Product[] => {
  return product.filter(p => p.categorySlug === categorySlug);
};

// Helper function to get product by slug
export const getProductBySlug = (slug: string): Product | undefined => {
  return product.find(p => p.slug === slug);
};
