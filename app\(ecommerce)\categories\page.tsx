"use client";

import { motion, AnimatePresence } from "framer-motion";
import { ChevronR<PERSON>, ArrowRight, ArrowDownCircle, Sparkles } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { getAllCategories } from "@/data/categories";

export default function CategoriesPage() {
  const categories = getAllCategories();

  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 300, damping: 24 } }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 via-white to-gray-50">
      {/* Breadcrumbs */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-white/70 backdrop-blur-md sticky top-0 z-10 border-b border-gray-200"
      >
        <div className="container mx-auto px-4 py-3 max-w-7xl">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-gray-900 transition-colors flex items-center gap-1">
              <span>Home</span>
            </Link>
            <ChevronRight className="h-3.5 w-3.5 mx-2 text-gray-400" />
            <span className="text-gray-900 font-medium">Categories</span>
          </div>
        </div>
      </motion.div>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="relative overflow-hidden py-20 sm:py-24"
      >
        <div className="absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-white to-transparent"></div>
        <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)] [background-size:16px_16px] z-0"></div>

        <div className="container relative z-10 mx-auto px-4 max-w-5xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-1.5 mb-6 px-3 py-1 rounded-full bg-indigo-50 text-indigo-700 text-sm font-medium"
          >
            <Sparkles className="h-3.5 w-3.5" />
            <span>Explore our collections</span>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600"
          >
            Shop by Category
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg text-gray-600 max-w-2xl mx-auto mb-8"
          >
            Discover our carefully curated collections designed to match your unique style and performance needs.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="flex justify-center"
          >
            <Link href="#categories">
              <Button
                variant="outline"
                size="lg"
                className="rounded-full border-gray-300 flex items-center gap-2 hover:bg-gray-100 hover:border-gray-400 transition-all"
              >
                <span>Scroll to explore</span>
                <ArrowDownCircle className="h-4 w-4 animate-bounce" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </motion.div>

      {/* Categories Grid */}
      <section id="categories" className="py-16 px-4">
        <div className="container mx-auto max-w-7xl">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.2 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {categories.map((category, index) => (
              <motion.div key={category.id} variants={itemVariants} className="h-full">
                <Link href={`/categories/${category.slug}`} className="block h-full">                  <Card className="h-full overflow-hidden group relative bg-white hover:shadow-xl transition-all duration-500 border-none shadow-lg ring-1 ring-gray-200">
                  {/* Glass blur effect at the top */}
                  <div className="absolute top-0 inset-x-0 h-24 bg-gradient-to-b from-white/80 to-transparent backdrop-blur-[2px] z-10"></div>

                  {/* Background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white opacity-50"></div>

                  {/* Subtle patterns */}
                  <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                  <div className="relative z-20">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center shadow-inner">
                          <div className="w-5 h-5 bg-gray-800 rounded-full shadow-md"></div>
                        </div>
                        {category.featured && (
                          <Badge className="bg-indigo-600 hover:bg-indigo-700 text-white px-2 py-0.5">
                            Featured
                          </Badge>
                        )}
                      </div>

                      <CardTitle className="text-2xl font-bold text-gray-900 mt-3 group-hover:text-indigo-600 transition-colors">
                        {category.name}
                      </CardTitle>

                      <CardDescription className="text-gray-600 line-clamp-2 text-sm">
                        {category.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="relative py-4">
                      {/* Product Image with Animation */}
                      <div className="absolute -right-8 top-1/2 -translate-y-1/2 transition-all duration-700 group-hover:scale-110 group-hover:-translate-y-[60%]">
                        <div className="relative h-44 w-44">
                          <Image
                            src={category.image}
                            alt={category.name}
                            fill
                            className="object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-xl"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                        </div>
                      </div>
                    </CardContent>

                    <CardFooter className="pt-4">
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm font-medium px-3 py-1 rounded-full text-indigo-600 bg-indigo-50">
                          {category.productCount} Products
                        </span>

                        <Button
                          size="sm"
                          variant="outline"
                          className="rounded-full border-indigo-600 text-indigo-600 hover:bg-indigo-600 hover:text-white transition-all duration-300 group-hover:opacity-100 opacity-80 font-medium"
                        >
                          <span>Explore</span>
                          <ArrowRight className="h-3.5 w-3.5 ml-1" />
                        </Button>
                      </div>
                    </CardFooter>
                  </div>

                  {/* Bottom gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-indigo-500/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Card>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="relative py-20"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-gray-900 to-gray-800"></div>
        <div className="absolute inset-0 bg-[url('/texture.png')] opacity-10 mix-blend-soft-light"></div>

        <div className="container relative z-10 mx-auto px-4 max-w-5xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 leading-tight">
              Can't find what you're looking for?
            </h2>
            <p className="text-gray-300 text-lg mb-8">
              Browse our complete collection or get in touch with our team for personalized recommendations.
            </p>
            <div className="flex flex-wrap gap-4 justify-center">
              <AnimatePresence>
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                >
                  <Link href="/products">
                    <Button size="lg" className="rounded-full bg-white text-gray-900 hover:bg-gray-100 shadow-lg shadow-white/10">
                      View All Products
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: 0.4 }}
                >
                  <Link href="/contact">
                    <Button
                      size="lg"
                      variant="outline"
                      className="rounded-full border-white/60 text-white hover:bg-white hover:text-gray-900 transition-colors shadow-lg shadow-black/5"
                    >
                      Contact Us
                    </Button>
                  </Link>
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
}
