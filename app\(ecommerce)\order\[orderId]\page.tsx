"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Package,
  Truck,
  CheckCircle,
  Clock,
  MapPin,
  Phone,
  Mail,
  Download,
  RefreshCw,
  Star,
  MessageCircle,
  Share2,
  Copy,
  Calendar,
  CreditCard,
  User,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// Mock order data - in real app this would come from API
const mockOrder = {
  id: "ORD-2024-001234",
  status: "shipped",
  orderDate: "2024-01-15T10:30:00Z",
  estimatedDelivery: "2024-01-20T18:00:00Z",
  total: 156.99,
  subtotal: 139.99,
  shipping: 9.99,
  tax: 7.01,
  paymentMethod: "•••• •••• •••• 4242",
  trackingNumber: "1Z999AA1234567890",
  items: [
    {
      id: "1",
      name: "Radiant Glow Serum",
      image: "https://images.unsplash.com/photo-1620916566398-39f1143ab7be?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      price: 89.99,
      quantity: 1,
      size: "30ml",
      color: "Clear",
    },
    {
      id: "2", 
      name: "Luxury Face Cream",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      price: 49.99,
      quantity: 1,
      size: "50ml",
      color: "White",
    },
  ],
  shippingAddress: {
    name: "Sarah Johnson",
    street: "123 Beauty Lane",
    city: "Los Angeles",
    state: "CA",
    zip: "90210",
    country: "United States",
    phone: "+****************",
    email: "<EMAIL>",
  },
  timeline: [
    {
      status: "Order Placed",
      date: "2024-01-15T10:30:00Z",
      completed: true,
      description: "Your order has been received and is being processed",
    },
    {
      status: "Payment Confirmed",
      date: "2024-01-15T10:35:00Z", 
      completed: true,
      description: "Payment has been successfully processed",
    },
    {
      status: "Processing",
      date: "2024-01-16T09:00:00Z",
      completed: true,
      description: "Your items are being prepared for shipment",
    },
    {
      status: "Shipped",
      date: "2024-01-17T14:20:00Z",
      completed: true,
      description: "Your order is on its way",
    },
    {
      status: "Out for Delivery",
      date: null,
      completed: false,
      description: "Your order is out for delivery",
    },
    {
      status: "Delivered",
      date: null,
      completed: false,
      description: "Your order has been delivered",
    },
  ],
};

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800",
  processing: "bg-blue-100 text-blue-800", 
  shipped: "bg-purple-100 text-purple-800",
  delivered: "bg-green-100 text-green-800",
  cancelled: "bg-red-100 text-red-800",
};

export default function OrderPage() {
  const params = useParams();
  const { toast } = useToast();
  const [order, setOrder] = useState(mockOrder);
  const [isLoading, setIsLoading] = useState(false);

  const copyOrderId = () => {
    navigator.clipboard.writeText(order.id);
    toast({
      title: "Order ID copied",
      description: "Order ID has been copied to clipboard",
    });
  };

  const copyTrackingNumber = () => {
    navigator.clipboard.writeText(order.trackingNumber);
    toast({
      title: "Tracking number copied", 
      description: "Tracking number has been copied to clipboard",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long", 
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Link href="/my-orders" className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4">
            <ArrowLeft className="h-4 w-4" />
            Back to Orders
          </Link>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Order Details</h1>
              <div className="flex items-center gap-2 mt-2">
                <span className="text-gray-600">Order ID:</span>
                <span className="font-mono text-sm">{order.id}</span>
                <Button variant="ghost" size="sm" onClick={copyOrderId}>
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge className={statusColors[order.status as keyof typeof statusColors]}>
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </Badge>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Invoice
              </Button>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Timeline */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h2 className="text-xl font-semibold mb-6">Order Timeline</h2>
              <div className="space-y-4">
                {order.timeline.map((step, index) => (
                  <div key={index} className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        step.completed ? "bg-green-100 text-green-600" : "bg-gray-100 text-gray-400"
                      }`}>
                        {step.completed ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <Clock className="h-4 w-4" />
                        )}
                      </div>
                      {index < order.timeline.length - 1 && (
                        <div className={`w-px h-8 mt-2 ${
                          step.completed ? "bg-green-200" : "bg-gray-200"
                        }`} />
                      )}
                    </div>
                    <div className="flex-1 pb-8">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className={`font-medium ${
                          step.completed ? "text-gray-900" : "text-gray-500"
                        }`}>
                          {step.status}
                        </h3>
                        {step.date && (
                          <span className="text-sm text-gray-500">
                            {formatDate(step.date)}
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Order Items */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h2 className="text-xl font-semibold mb-6">Order Items</h2>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex gap-4 p-4 border rounded-lg">
                    <div className="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Size: {item.size} • Color: {item.color}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm text-gray-600">Qty: {item.quantity}</span>
                        <span className="font-medium">${item.price}</span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2">
                      <Button variant="outline" size="sm">
                        <Star className="h-3 w-3 mr-1" />
                        Review
                      </Button>
                      <Button variant="outline" size="sm">
                        Buy Again
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Tracking Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold mb-4">Tracking Information</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Tracking Number:</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-mono text-sm">{order.trackingNumber}</span>
                  <Button variant="ghost" size="sm" onClick={copyTrackingNumber}>
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <Button className="w-full mt-4">
                  <Truck className="h-4 w-4 mr-2" />
                  Track Package
                </Button>
              </div>
            </motion.div>

            {/* Order Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span>${order.subtotal}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>${order.shipping}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span>${order.tax}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>${order.total}</span>
                </div>
              </div>
            </motion.div>

            {/* Shipping Address */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold mb-4">Shipping Address</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>{order.shippingAddress.name}</span>
                </div>
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div>
                    <div>{order.shippingAddress.street}</div>
                    <div>
                      {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zip}
                    </div>
                    <div>{order.shippingAddress.country}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{order.shippingAddress.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{order.shippingAddress.email}</span>
                </div>
              </div>
            </motion.div>

            {/* Payment Method */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold mb-4">Payment Method</h3>
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{order.paymentMethod}</span>
              </div>
            </motion.div>

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="bg-white rounded-lg shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold mb-4">Need Help?</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
                <Button variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Return Items
                </Button>
                <Button variant="outline" className="w-full">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Order
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
