"use client";

import { addToCart, addToWishlist } from "@/actions/cart-actions";
import SubmitButton from "@/components/submitButton";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { product } from "@/data/product";
import Link from "next/link";
import {
  ArrowRight,
  Check,
  ChevronRight,
  Clock,
  Eye,
  Heart,
  HelpCircle,
  MessageCircle,
  Share2,
  ShoppingBag,
  ShoppingCart,
  Star,
  Truck,
  Zap,
  AlertCircle,
  Calendar,
  Thermometer,
  Pill,
  ShieldAlert,
  Award,
  Leaf,
  HeartPulse,
  UserCog,
  Grid,
  List,
  Maximize2,
  Play,
  Volume2,
  Info,
  Package,
  Shield,
  RotateCcw,
  Users,
  Globe,
  CheckCircle,
  Quote,
  Instagram,
  Facebook,
  Twitter,
  TrendingUp,
  Sparkles,
  Layers,
  Palette,
  Camera,
  Filter,
  Search,
  Bookmark,
  Download,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Plus,
  Minus,
  X,
  Menu,
  Home,
  ShoppingCartIcon,
  User,
  Settings,
  Mail,
  Phone,
  MapPin,
  CreditCard,
  Gift,
  Percent,
  Target,
  Lightbulb,
  Headphones,
  FileText,
  Image,
  Video,
  Music,
  Mic,
  Speaker,
  Wifi,
  Battery,
  Bluetooth,
  Usb,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  Watch,
  Camera as CameraIcon,
  Gamepad2,
  Tv,
  Radio,
  Printer,
  Scanner,
  Keyboard,
  Mouse,
  Webcam,
  Headset,
  Microphone,
  Router,
  HardDrive,
  Cpu,
  MemoryStick,
  SdCard,
  UsbIcon,
  PowerIcon,
  BatteryIcon,
  WifiIcon,
  BluetoothIcon,
  VolumeIcon,
  BrightnessIcon,
  ContrastIcon,
  SaturationIcon,
  HueIcon,
  TemperatureIcon,
  HumidityIcon,
  PressureIcon,
  WindIcon,
  RainIcon,
  SnowIcon,
  SunIcon,
  MoonIcon,
  CloudIcon,
  StormIcon,
  FogIcon,
  MistIcon,
  HazeIcon,
  SmokeIcon,
  DustIcon,
  SandIcon,
  AshIcon,
  SquallIcon,
  TornadoIcon,
  ClearIcon,
  FewCloudsIcon,
  ScatteredCloudsIcon,
  BrokenCloudsIcon,
  ShowerRainIcon,
  RainIcon as RainIconAlt,
  ThunderstormIcon,
  SnowIcon as SnowIconAlt,
  MistIcon as MistIconAlt,
} from "lucide-react";
import { useState, useEffect } from "react";
import { Tab, TabList, TabPanel, Tabs } from "react-aria-components";
import { useFormState, useFormStatus } from "react-dom";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { useCart } from "@/lib/CartContext";
import { useSearchParams } from "next/navigation";

// Custom AddToCartButton component
function AddToCartButton({ product, quantity, selectedSize, selectedColor }) {
  const { addItem } = useCart();
  const { toast } = useToast();
  const { pending } = useFormStatus();

  const handleAddToCart = async () => {
    if (!selectedSize && product.sizes && product.sizes.length > 0) {
      toast({
        title: "Please select a size",
        description: "You need to select a size before adding to cart",
        variant: "destructive",
      });
      return;
    }

    if (!selectedColor && product.colors && product.colors.length > 0) {
      toast({
        title: "Please select a color",
        description: "You need to select a color before adding to cart",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await addToCart({
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: quantity,
        image: product.image,
        size: selectedSize,
        color: selectedColor,
      });

      if (result.success) {
        // Add to client-side cart
        addItem({
          id: result.product.id,
          productId: result.product.productId,
          name: result.product.name,
          price: result.product.price,
          quantity: result.product.quantity,
          image: result.product.image,
          size: result.product.size,
          color: result.product.color,
        });

        toast({
          title: "Added to cart",
          description: `${product.name} has been added to your cart`,
        });
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add product to cart",
        variant: "destructive",
      });
    }
  };

  return (
    <Button
      onClick={handleAddToCart}
      disabled={pending}
      data-add-to-cart
      className="w-full py-6 bg-black hover:bg-gray-800 text-white flex items-center justify-center gap-2 rounded-md"
    >
      <ShoppingBag className="w-5 h-5" />
      {pending ? "Adding..." : "Add to Cart"}
    </Button>
  );
}

export default function Product({ params }: any) {
  const searchParams = useSearchParams();
  const heroVariant = searchParams.get("hero") || "1"; // Default to variant 1

  const targetProduct = product.filter((p) => p.slug === params.slug)[0];
  const [currentImage, setCurrentImage] = useState(targetProduct.image);
  const [cartCount, setCartCount] = useState(1);
  const [viewersCount, setViewersCount] = useState(
    Math.floor(Math.random() * 30) + 5
  );
  const [timeLeft, setTimeLeft] = useState(24 * 60 * 60); // 24 hours in seconds
  const [selectedSize, setSelectedSize] = useState("");
  const [selectedColor, setSelectedColor] = useState("");
  const { toast } = useToast();
  const { addToWishlist } = useCart();

  // Format time for countdown display
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulate random viewers
  useEffect(() => {
    const interval = setInterval(() => {
      const change = Math.random() > 0.5 ? 1 : -1;
      setViewersCount((prev) => Math.max(5, prev + change));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Filter similar products (same category or price range)
  const similarProducts = product
    .filter(
      (p) =>
        p.id !== targetProduct.id &&
        Math.abs(p.price - targetProduct.price) < 20
    )
    .slice(0, 4);

  // Render variant based on query parameter
  const renderProductVariant = () => {
    const commonProps = {
      targetProduct,
      currentImage,
      setCurrentImage,
      cartCount,
      setCartCount,
      viewersCount,
      timeLeft,
      formatTime,
      selectedSize,
      setSelectedSize,
      selectedColor,
      setSelectedColor,
      toast,
      addToWishlist,
      similarProducts,
    };

    switch (heroVariant) {
      case "1":
        return <ProductVariant1 {...commonProps} />;
      case "2":
        return <ProductVariant2 {...commonProps} />;
      case "3":
        return <ProductVariant3 {...commonProps} />;
      case "4":
        return <ProductVariant4 {...commonProps} />;
      case "5":
        return <ProductVariant5 {...commonProps} />;
      case "6":
        return <ProductVariant6 {...commonProps} />;
      case "7":
        return <ProductVariant7 {...commonProps} />;
      case "8":
        return <ProductVariant8 {...commonProps} />;
      case "9":
        return <ProductVariant9 {...commonProps} />;
      case "10":
        return <ProductVariant10 {...commonProps} />;
      case "11":
        return <ProductVariant11 {...commonProps} />;
      case "12":
        return <ProductVariant12 {...commonProps} />;
      case "13":
        return <ProductVariant13 {...commonProps} />;
      case "14":
        return <ProductVariant14 {...commonProps} />;
      case "15":
        return <ProductVariant15 {...commonProps} />;
      default:
        return <ProductVariant1 {...commonProps} />;
    }
  };

  return renderProductVariant();
}

// Product Variant 1 - Original Design (Current)
function ProductVariant1({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="bg-gray-50">
      {/* Breadcrumbs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-3 max-w-7xl">
          <div className="flex items-center text-sm text-gray-500">
            <Link href="/" className="hover:text-black transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link
              href="/products"
              className="hover:text-black transition-colors"
            >
              Products
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-gray-900 font-medium">
              {targetProduct.name}
            </span>
          </div>
        </div>
      </div>

      {/* Main Product Section */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Product Images - Left Column */}
            <div className="lg:w-1/2 p-6 border-r border-gray-100">
              {/* Desktop View */}
              <div className="hidden md:block">
                {/* Main Image with Hover Zoom */}
                <div className="relative mb-6 h-[500px] bg-gray-50 rounded-xl overflow-hidden">
                  <div className="w-full h-full group">
                    <img
                      src={currentImage}
                      alt={targetProduct.name}
                      className="w-full h-full object-contain p-4 transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-5 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <span className="bg-white bg-opacity-75 text-black text-xs px-2 py-1 rounded">
                        Hover to zoom
                      </span>
                    </div>
                  </div>

                  {/* Product Badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    {targetProduct.price > 80 && (
                      <Badge className="bg-black text-white px-3 py-1 text-sm font-medium rounded-full">
                        PREMIUM
                      </Badge>
                    )}
                    <Badge className="bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded-full">
                      NEW
                    </Badge>
                    {targetProduct.price > 90 && (
                      <Badge className="bg-amber-500 text-white px-3 py-1 text-sm font-medium rounded-full">
                        BESTSELLER
                      </Badge>
                    )}
                  </div>

                  {/* Discount Badge */}
                  <Badge className="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 text-sm font-medium rounded-full">
                    -20%
                  </Badge>
                </div>

                {/* Thumbnails */}
                <div className="grid grid-cols-5 gap-4 h-24">
                  <div
                    onClick={() => setCurrentImage(targetProduct.image)}
                    className={`border rounded-lg cursor-pointer overflow-hidden ${
                      currentImage === targetProduct.image
                        ? "ring-2 ring-black"
                        : "hover:border-gray-400"
                    }`}
                  >
                    <img
                      src={targetProduct.image}
                      alt="Main"
                      className="w-full h-full object-cover object-center"
                    />
                  </div>
                  {targetProduct.subImages.map((img, i) => (
                    <div
                      key={i}
                      onClick={() => setCurrentImage(img)}
                      className={`border rounded-lg cursor-pointer overflow-hidden ${
                        currentImage === img
                          ? "ring-2 ring-black"
                          : "hover:border-gray-400"
                      }`}
                    >
                      <img
                        src={img}
                        alt={`View ${i + 1}`}
                        className="w-full h-full object-cover object-center"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Mobile View - Carousel */}
              <div className="md:hidden">
                <Carousel className="w-full">
                  <CarouselContent>
                    <CarouselItem>
                      <div className="relative h-[350px] bg-gray-50 rounded-xl overflow-hidden">
                        <img
                          src={targetProduct.image}
                          alt={targetProduct.name}
                          className="w-full h-full object-contain p-4"
                        />
                        {/* Product Badges */}
                        <div className="absolute top-4 left-4 flex flex-col gap-2">
                          {targetProduct.price > 80 && (
                            <Badge className="bg-black text-white px-3 py-1 text-sm font-medium rounded-full">
                              PREMIUM
                            </Badge>
                          )}
                          <Badge className="bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded-full">
                            NEW
                          </Badge>
                        </div>
                      </div>
                    </CarouselItem>
                    {targetProduct.subImages.map((img, i) => (
                      <CarouselItem key={i}>
                        <div className="relative h-[350px] bg-gray-50 rounded-xl overflow-hidden">
                          <img
                            src={img}
                            alt={`${targetProduct.name} view ${i + 1}`}
                            className="w-full h-full object-contain p-4"
                          />
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  <CarouselPrevious className="left-2" />
                  <CarouselNext className="right-2" />
                </Carousel>

                {/* Mobile Thumbnails */}
                <div className="grid grid-cols-5 gap-2 mt-4 h-16">
                  <div
                    onClick={() => setCurrentImage(targetProduct.image)}
                    className={`border rounded-lg cursor-pointer overflow-hidden ${
                      currentImage === targetProduct.image
                        ? "ring-2 ring-black"
                        : "hover:border-gray-400"
                    }`}
                  >
                    <img
                      src={targetProduct.image}
                      alt="Main"
                      className="w-full h-full object-cover object-center"
                    />
                  </div>
                  {targetProduct.subImages.map((img, i) => (
                    <div
                      key={i}
                      onClick={() => setCurrentImage(img)}
                      className={`border rounded-lg cursor-pointer overflow-hidden ${
                        currentImage === img
                          ? "ring-2 ring-black"
                          : "hover:border-gray-400"
                      }`}
                    >
                      <img
                        src={img}
                        alt={`View ${i + 1}`}
                        className="w-full h-full object-cover object-center"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Sharing */}
              <div className="mt-6 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Share:</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="text-blue-600"
                    >
                      <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
                    </svg>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="text-blue-800"
                    >
                      <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
                    </svg>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full h-8 w-8"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="text-pink-600"
                    >
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                    </svg>
                  </Button>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={async () => {
                    try {
                      const result = await addToWishlist({
                        productId: targetProduct.id,
                        name: targetProduct.name,
                        price: targetProduct.price,
                        image: targetProduct.image,
                      });

                      if (result.success) {
                        // Add to client-side wishlist
                        addToWishlist({
                          id: result.product.id,
                          productId: result.product.productId,
                          name: result.product.name,
                          price: result.product.price,
                          image: result.product.image,
                          dateAdded: result.product.dateAdded,
                        });

                        toast({
                          title: "Added to wishlist",
                          description: `${targetProduct.name} has been added to your wishlist`,
                        });
                      } else {
                        toast({
                          title: "Error",
                          description: result.message,
                          variant: "destructive",
                        });
                      }
                    } catch (error) {
                      toast({
                        title: "Error",
                        description: "Failed to add product to wishlist",
                        variant: "destructive",
                      });
                    }
                  }}
                >
                  <Heart className="h-4 w-4" />
                  <span>Save</span>
                </Button>
              </div>
            </div>

            {/* Product Details - Right Column */}
            <div className="lg:w-1/2 p-6">
              <div className="max-w-xl">
                {/* Product Title and Rating */}
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className="bg-green-500 text-white px-2 py-0.5 text-xs rounded-sm">
                      IN STOCK
                    </Badge>
                    <span className="text-sm text-gray-500">
                      SKU: {targetProduct.id.toString().padStart(6, "0")}
                    </span>
                  </div>

                  <h1 className="text-3xl font-bold mb-3">
                    {targetProduct.name}
                  </h1>

                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-yellow-400" />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600 ml-2">
                        4.8 (23 Reviews)
                      </span>
                    </div>

                    <div className="flex items-center text-gray-600 text-sm">
                      <Eye className="w-4 h-4 mr-1" />
                      <span>{viewersCount} viewing now</span>
                    </div>
                  </div>
                </div>

                {/* Price Section */}
                <div className="mb-6">
                  <div className="flex items-center mb-2">
                    <span className="text-3xl font-bold mr-3 text-gray-900">
                      ${targetProduct.price}.00
                    </span>
                    <span className="text-gray-500 line-through text-lg">
                      ${(targetProduct.price * 1.2).toFixed(2)}
                    </span>
                    <span className="ml-3 bg-red-100 text-red-700 px-2 py-1 text-xs font-medium rounded">
                      SAVE 20%
                    </span>
                  </div>

                  {/* Limited Time Offer */}
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-2 text-amber-800 font-medium mb-2">
                      <Clock className="w-4 h-4" />
                      <span className="text-sm">Flash Sale Ends In:</span>
                    </div>
                    <div className="flex gap-2 text-center">
                      <div className="bg-amber-800 text-white rounded px-2 py-1 w-14">
                        <div className="text-lg font-bold">
                          {formatTime(timeLeft).split(":")[0]}
                        </div>
                        <div className="text-xs">Hours</div>
                      </div>
                      <div className="bg-amber-800 text-white rounded px-2 py-1 w-14">
                        <div className="text-lg font-bold">
                          {formatTime(timeLeft).split(":")[1]}
                        </div>
                        <div className="text-xs">Mins</div>
                      </div>
                      <div className="bg-amber-800 text-white rounded px-2 py-1 w-14">
                        <div className="text-lg font-bold">
                          {formatTime(timeLeft).split(":")[2]}
                        </div>
                        <div className="text-xs">Secs</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Medical Disclaimer */}
                <div className="mb-4">
                  <Alert className="bg-gray-50 border-gray-200">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs text-gray-600">
                      This product is not intended to diagnose, treat, cure, or
                      prevent any disease. Results may vary. Consult with a
                      healthcare professional before use if you have any medical
                      conditions.
                    </AlertDescription>
                  </Alert>
                </div>

                {/* Certification Badges */}
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Dermatologically Tested</span>
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Paraben Free</span>
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-purple-50 text-purple-700 border-purple-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Cruelty Free</span>
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1"
                  >
                    <Award className="h-3 w-3" />
                    <span>Natural Ingredients</span>
                  </Badge>
                </div>

                {/* Short Description */}
                <div className="mb-6">
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {targetProduct.description.substring(0, 150)}...
                  </p>
                </div>

                {/* Size Selector */}
                {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      Size
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {targetProduct.sizes.map((size) => (
                        <button
                          key={size}
                          className={`px-4 py-2 border rounded-md text-sm ${
                            selectedSize === size
                              ? "border-black bg-black text-white"
                              : "border-gray-300 hover:border-gray-400"
                          }`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selector */}
                {targetProduct.colors && targetProduct.colors.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      Color
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {targetProduct.colors.map((color) => (
                        <button
                          key={color}
                          className={`px-4 py-2 border rounded-md text-sm ${
                            selectedColor === color
                              ? "border-black bg-black text-white"
                              : "border-gray-300 hover:border-gray-400"
                          }`}
                          onClick={() => setSelectedColor(color)}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity Selector */}
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">
                    Quantity
                  </h3>
                  <div className="flex">
                    <button
                      className="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-l-md hover:bg-gray-100"
                      onClick={() =>
                        cartCount > 1 && setCartCount(cartCount - 1)
                      }
                    >
                      -
                    </button>
                    <input
                      type="text"
                      value={cartCount}
                      onChange={(e) => {
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value > 0) {
                          setCartCount(value);
                        }
                      }}
                      className="w-16 h-10 text-center border-t border-b border-gray-300"
                    />
                    <button
                      className="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-r-md hover:bg-gray-100"
                      onClick={() => setCartCount(cartCount + 1)}
                    >
                      +
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4 mb-6">
                  <div className="flex-1">
                    <AddToCartButton
                      product={targetProduct}
                      quantity={cartCount}
                      selectedSize={selectedSize}
                      selectedColor={selectedColor}
                    />
                  </div>
                  <Button
                    className="w-full py-6 bg-amber-600 hover:bg-amber-700 text-white flex items-center justify-center gap-2 rounded-md"
                    onClick={() => {
                      // First add to cart, then redirect to checkout
                      const addToCartBtn =
                        document.querySelector("[data-add-to-cart]");
                      if (addToCartBtn) {
                        addToCartBtn.click();
                        setTimeout(() => {
                          window.location.href = "/checkout";
                        }, 500);
                      }
                    }}
                  >
                    <Zap className="w-5 h-5" />
                    Buy Now
                  </Button>
                </div>

                {/* Shipping Info */}
                <div className="border border-gray-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center text-gray-700 mb-3">
                    <Truck className="w-5 h-5 mr-2 text-green-600" />
                    <span className="font-medium">Free shipping</span>
                    <span className="text-sm ml-1">on orders over $100</span>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      30-day returns
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      Secure checkout
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      Authentic products
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      Worldwide shipping
                    </div>
                  </div>
                </div>

                {/* Health Information */}
                <div className="border border-gray-200 rounded-lg p-4 mb-6 bg-blue-50/30">
                  <div className="flex items-center text-blue-700 mb-3">
                    <HeartPulse className="w-5 h-5 mr-2" />
                    <span className="font-medium">Health Information</span>
                  </div>
                  <div className="text-sm text-gray-700 space-y-2">
                    <p>
                      Always read the label before use. Not suitable for
                      children under 12.
                    </p>
                    <p>
                      Consult with a healthcare professional if symptoms
                      persist.
                    </p>
                  </div>
                </div>

                {/* Pharmacist Consultation */}
                <div className="mb-6">
                  <Button
                    variant="outline"
                    className="w-full py-3 border-blue-200 text-blue-700 hover:bg-blue-50 flex items-center justify-center gap-2"
                  >
                    <UserCog className="w-5 h-5" />
                    <span>Consult with a Pharmacist</span>
                  </Button>
                </div>

                {/* Ask a Question */}
                <div className="flex items-center justify-between">
                  <Button
                    variant="link"
                    className="text-blue-600 p-0 h-auto flex items-center gap-1"
                  >
                    <HelpCircle className="w-4 h-4" />
                    <span>Ask a Question</span>
                  </Button>
                  <Button
                    variant="link"
                    className="text-blue-600 p-0 h-auto flex items-center gap-1"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span>Chat with Us</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Tabs Section */}
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          <div className="bg-white rounded-xl shadow-sm overflow-hidden p-6 mb-8">
            <Tabs className="w-full flex flex-col gap-2">
              <TabList
                aria-label="Product Information"
                className="flex border-b"
              >
                <Tab
                  id="description"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Description
                </Tab>
                <Tab
                  id="ingredients"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Ingredients
                </Tab>
                <Tab
                  id="usage"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Usage & Dosage
                </Tab>
                <Tab
                  id="specifications"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Specifications
                </Tab>
                <Tab
                  id="shipping"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Shipping & Returns
                </Tab>
                <Tab
                  id="reviews"
                  className="px-6 py-3 text-lg cursor-pointer font-medium text-gray-500 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-900 focus:outline-none"
                >
                  Reviews (43)
                </Tab>
              </TabList>

              <TabPanel id="description" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">
                    Product Description
                  </h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed mb-4">
                      {targetProduct.description}
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      Our products are crafted with premium materials to ensure
                      durability and comfort. Each item undergoes rigorous
                      quality testing before reaching our customers.
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      The {targetProduct.name} is designed to provide
                      exceptional performance and value. Whether you're using it
                      at home, at work, or on the go, this product will exceed
                      your expectations with its reliability and effectiveness.
                    </p>
                  </div>

                  <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Check className="w-5 h-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">Premium Quality</h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Made with the highest quality materials for durability
                        and performance.
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Check className="w-5 h-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">Eco-Friendly</h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Sustainably sourced and produced with minimal
                        environmental impact.
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Check className="w-5 h-5 text-green-600 mr-2" />
                        <h3 className="font-semibold">
                          Satisfaction Guaranteed
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Try risk-free with our 30-day money-back guarantee
                        policy.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="ingredients" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">Ingredients</h2>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <Leaf className="w-5 h-5 mr-2 text-green-600" />
                      Active Ingredients
                    </h3>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700">
                      <li>Aloe Vera (20%) - Soothing and cooling properties</li>
                      <li>Menthol (5%) - Provides cooling sensation</li>
                      <li>Eucalyptus Oil (2%) - Natural anti-inflammatory</li>
                      <li>
                        Arnica Extract (1%) - Helps reduce bruising and swelling
                      </li>
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3">
                      Other Ingredients
                    </h3>
                    <p className="text-gray-700 mb-3">
                      Purified Water, Glycerin, Cetyl Alcohol, Stearic Acid,
                      Dimethicone, Glyceryl Stearate, PEG-100 Stearate,
                      Carbomer, Triethanolamine, Phenoxyethanol,
                      Ethylhexylglycerin.
                    </p>
                  </div>

                  <Alert className="bg-amber-50 border-amber-200 mb-6">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <AlertTitle className="text-amber-800">
                      Allergen Information
                    </AlertTitle>
                    <AlertDescription className="text-amber-700">
                      This product contains eucalyptus oil which may cause
                      allergic reactions in some individuals. If you have known
                      allergies to botanical ingredients, please perform a patch
                      test before use.
                    </AlertDescription>
                  </Alert>

                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex items-center justify-center p-2 bg-green-100 rounded-full">
                      <Award className="w-5 h-5 text-green-600" />
                    </div>
                    <span className="text-gray-700">Paraben-free</span>

                    <div className="flex items-center justify-center p-2 bg-green-100 rounded-full">
                      <Award className="w-5 h-5 text-green-600" />
                    </div>
                    <span className="text-gray-700">Cruelty-free</span>

                    <div className="flex items-center justify-center p-2 bg-green-100 rounded-full">
                      <Award className="w-5 h-5 text-green-600" />
                    </div>
                    <span className="text-gray-700">
                      Dermatologically tested
                    </span>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="usage" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">Usage & Dosage</h2>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <Pill className="w-5 h-5 mr-2 text-blue-600" />
                      Recommended Usage
                    </h3>
                    <div className="prose max-w-none text-gray-700">
                      <p className="mb-3">
                        Apply a thin layer of Forever Aloe Cooling Lotion to
                        affected areas up to 3-4 times daily, or as needed for
                        relief. Gently massage into skin until absorbed.
                      </p>
                      <p>
                        For best results, apply after physical activity or
                        whenever cooling relief is desired. Allow to dry
                        completely before covering with clothing.
                      </p>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <HeartPulse className="w-5 h-5 mr-2 text-blue-600" />
                      Health Benefits
                    </h3>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700">
                      <li>
                        Provides cooling relief to sore muscles and joints
                      </li>
                      <li>Helps reduce inflammation and discomfort</li>
                      <li>Moisturizes and soothes dry, irritated skin</li>
                      <li>Promotes faster recovery after physical exertion</li>
                      <li>Improves circulation in applied areas</li>
                    </ul>
                  </div>

                  <Alert className="bg-red-50 border-red-200 mb-6">
                    <ShieldAlert className="h-4 w-4 text-red-600" />
                    <AlertTitle className="text-red-800">
                      Warnings & Contraindications
                    </AlertTitle>
                    <AlertDescription className="text-red-700">
                      <ul className="list-disc pl-5 space-y-1 mt-2">
                        <li>For external use only. Avoid contact with eyes.</li>
                        <li>Do not apply to broken or irritated skin.</li>
                        <li>Not recommended for children under 12 years.</li>
                        <li>Discontinue use if irritation occurs.</li>
                        <li>
                          Consult a healthcare professional before use if
                          pregnant or nursing.
                        </li>
                        <li>
                          Do not use with heating pads or immediately
                          before/after hot showers.
                        </li>
                      </ul>
                    </AlertDescription>
                  </Alert>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Calendar className="w-5 h-5 text-gray-600 mr-2" />
                        <h3 className="font-semibold">
                          Expiration Information
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        This product has a shelf life of 24 months from the
                        manufacturing date when unopened. Once opened, use
                        within 12 months for optimal effectiveness. Expiration
                        date is printed on the bottom of the container.
                      </p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <Thermometer className="w-5 h-5 text-gray-600 mr-2" />
                        <h3 className="font-semibold">
                          Storage Recommendations
                        </h3>
                      </div>
                      <p className="text-sm text-gray-600">
                        Store in a cool, dry place away from direct sunlight.
                        Keep at temperatures between 5°C and 25°C (41°F and
                        77°F). For an enhanced cooling effect, refrigeration is
                        recommended but not required.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="specifications" className="py-6">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold mb-4">
                    Product Specifications
                  </h2>

                  <div className="overflow-hidden border border-gray-200 rounded-lg mb-6">
                    <table className="min-w-full divide-y divide-gray-200">
                      <tbody className="divide-y divide-gray-200">
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 w-1/3">
                            Brand
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Forever
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Model
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {targetProduct.name}
                          </td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Weight
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            250g
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Dimensions
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            10 × 5 × 3 cm
                          </td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Material
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Premium Aloe Vera
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Country of Origin
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            USA
                          </td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            Warranty
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            1 Year Limited Warranty
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-blue-600"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">
                          Note about specifications
                        </h3>
                        <div className="mt-1 text-sm text-blue-700">
                          <p>
                            Specifications may vary slightly from the listed
                            information. Please refer to the product packaging
                            for the most accurate details.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="shipping" className="py-6">
                <div className=" mx-auto">
                  <h2 className="text-2xl font-bold mb-4">
                    Shipping & Returns
                  </h2>

                  <div className="mb-8">
                    <h3 className="text-lg font-semibold mb-3">
                      Shipping Information
                    </h3>
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center">
                          <Truck className="w-5 h-5 text-green-600 mr-3" />
                          <div>
                            <h4 className="font-medium">Standard Shipping</h4>
                            <p className="text-sm text-gray-600">
                              3-5 business days
                            </p>
                          </div>
                          <div className="ml-auto font-medium">Free</div>
                        </div>
                      </div>
                      <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center">
                          <Truck className="w-5 h-5 text-amber-600 mr-3" />
                          <div>
                            <h4 className="font-medium">Express Shipping</h4>
                            <p className="text-sm text-gray-600">
                              1-2 business days
                            </p>
                          </div>
                          <div className="ml-auto font-medium">$9.99</div>
                        </div>
                      </div>
                      <div className="px-6 py-4">
                        <div className="flex items-center">
                          <Truck className="w-5 h-5 text-blue-600 mr-3" />
                          <div>
                            <h4 className="font-medium">
                              International Shipping
                            </h4>
                            <p className="text-sm text-gray-600">
                              7-10 business days
                            </p>
                          </div>
                          <div className="ml-auto font-medium">$19.99</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Return Policy
                    </h3>
                    <div className="prose max-w-none text-gray-700">
                      <p>
                        We want you to be completely satisfied with your
                        purchase. If for any reason you're not happy with your
                        order, we accept returns within 30 days of delivery.
                      </p>
                      <h4 className="text-base font-medium mt-4 mb-2">
                        To be eligible for a return, your item must be:
                      </h4>
                      <ul className="list-disc pl-5 space-y-1">
                        <li>In the same condition that you received it</li>
                        <li>Unworn or unused with tags still attached</li>
                        <li>In the original packaging</li>
                      </ul>
                      <h4 className="text-base font-medium mt-4 mb-2">
                        Return Process:
                      </h4>
                      <ol className="list-decimal pl-5 space-y-1">
                        <li>
                          Contact our customer service team to initiate a return
                        </li>
                        <li>
                          Pack the item securely in its original packaging
                        </li>
                        <li>Include your order number and return reason</li>
                        <li>
                          Ship the package to the address provided by customer
                          service
                        </li>
                      </ol>
                      <p className="mt-4">
                        Once we receive and inspect your return, we'll process
                        your refund. The money will be refunded to your original
                        payment method within 5-7 business days.
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="reviews" className="py-6">
                <div className="mx-auto">
                  <div className="flex flex-col md:flex-row gap-8 mb-8">
                    <div className="md:w-1/3">
                      <div className="bg-gray-50 p-6 rounded-lg">
                        <div className="text-center mb-4">
                          <div className="text-5xl font-bold text-gray-900">
                            4.8
                          </div>
                          <div className="flex justify-center mt-2">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className="w-5 h-5 fill-yellow-400 text-yellow-400"
                              />
                            ))}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            Based on 43 reviews
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center">
                            <span className="text-sm w-8">5★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "85%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">85%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">4★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "10%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">10%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">3★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "5%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">5%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">2★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "0%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">0%</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm w-8">1★</span>
                            <div className="flex-1 h-2 mx-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="bg-yellow-400 h-full rounded-full"
                                style={{ width: "0%" }}
                              ></div>
                            </div>
                            <span className="text-sm w-8 text-right">0%</span>
                          </div>
                        </div>

                        <div className="mt-6">
                          <Button className="w-full bg-black hover:bg-gray-800 text-white">
                            Write a Review
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="md:w-2/3">
                      <div className="space-y-6">
                        <ReviewItem
                          name="John Smith"
                          date="March 15, 2023"
                          rating={5}
                          verified={true}
                          content="This product exceeded my expectations! The quality is outstanding and it looks even better in person. Shipping was fast and the packaging was excellent. Highly recommend!"
                          image="https://randomuser.me/api/portraits/men/32.jpg"
                        />
                        <ReviewItem
                          name="Sarah Johnson"
                          date="February 28, 2023"
                          rating={5}
                          verified={true}
                          content="I've been using this product for a month now and I'm very impressed with the results. It's exactly as described and works perfectly for my needs. Customer service was also excellent when I had questions."
                          image="https://randomuser.me/api/portraits/women/44.jpg"
                        />
                        <ReviewItem
                          name="Michael Brown"
                          date="January 12, 2023"
                          rating={4}
                          verified={true}
                          content="Great product overall. The only reason I'm giving 4 stars instead of 5 is because the delivery took a bit longer than expected. Otherwise, the product itself is fantastic and works great."
                          image="https://randomuser.me/api/portraits/men/22.jpg"
                        />
                      </div>

                      <div className="mt-8 text-center">
                        <Button
                          variant="outline"
                          className="border-gray-300 rounded-md"
                        >
                          Load More Reviews
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabPanel>
            </Tabs>
          </div>

          {/* Similar Products Section */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarProducts.map((item) => (
                <Link
                  key={item.id}
                  href={`/products/${item.slug}`}
                  className="bg-white rounded-xl shadow-sm overflow-hidden group hover:shadow-md transition-all duration-300"
                >
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
                    />
                    {item.price > 90 && (
                      <Badge className="absolute top-2 left-2 bg-amber-500 text-white px-2 py-0.5 text-xs rounded-sm">
                        BESTSELLER
                      </Badge>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="flex text-yellow-400 mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400" />
                      ))}
                    </div>
                    <h3 className="font-medium mb-1 text-gray-900">
                      {item.name}
                    </h3>
                    <div className="flex items-center">
                      <span className="font-bold text-gray-900">
                        ${item.price}.00
                      </span>
                      <span className="ml-2 text-sm text-gray-500 line-through">
                        ${(item.price * 1.2).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recently Viewed Section */}
          <div>
            <h2 className="text-2xl font-bold mb-6">Recently Viewed</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {product.slice(0, 4).map((item) => (
                <Link
                  key={item.id}
                  href={`/products/${item.slug}`}
                  className="bg-white rounded-xl shadow-sm overflow-hidden group hover:shadow-md transition-all duration-300"
                >
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium mb-1 text-gray-900">
                      {item.name}
                    </h3>
                    <div className="flex items-center">
                      <span className="font-bold text-gray-900">
                        ${item.price}.00
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 2 - Minimalist Split-Screen Layout
function ProductVariant2({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-white">
      {/* Minimalist Breadcrumbs */}
      <div className="border-b border-gray-100">
        <div className="container mx-auto px-6 py-4 max-w-7xl">
          <div className="flex items-center text-sm text-gray-400">
            <Link href="/" className="hover:text-gray-600 transition-colors">
              Home
            </Link>
            <span className="mx-3">/</span>
            <Link
              href="/products"
              className="hover:text-gray-600 transition-colors"
            >
              Products
            </Link>
            <span className="mx-3">/</span>
            <span className="text-gray-900">{targetProduct.name}</span>
          </div>
        </div>
      </div>

      {/* Split Screen Layout */}
      <div className="flex flex-col lg:flex-row min-h-screen">
        {/* Left Side - Image Gallery */}
        <div className="lg:w-1/2 bg-gray-50 flex items-center justify-center p-8">
          <div className="max-w-lg w-full">
            {/* Main Image */}
            <div className="relative mb-6 aspect-square bg-white rounded-2xl overflow-hidden shadow-lg">
              <img
                src={currentImage}
                alt={targetProduct.name}
                className="w-full h-full object-contain p-8 transition-transform duration-500 hover:scale-105"
              />

              {/* Floating Badges */}
              <div className="absolute top-6 left-6">
                <Badge className="bg-black text-white px-4 py-2 text-sm font-medium rounded-full shadow-lg">
                  NEW
                </Badge>
              </div>

              <div className="absolute top-6 right-6">
                <Badge className="bg-red-500 text-white px-4 py-2 text-sm font-medium rounded-full shadow-lg">
                  -20%
                </Badge>
              </div>
            </div>

            {/* Thumbnail Gallery */}
            <div className="flex gap-3 justify-center">
              <div
                onClick={() => setCurrentImage(targetProduct.image)}
                className={`w-16 h-16 rounded-xl cursor-pointer overflow-hidden transition-all duration-300 ${
                  currentImage === targetProduct.image
                    ? "ring-2 ring-black shadow-lg"
                    : "hover:shadow-md"
                }`}
              >
                <img
                  src={targetProduct.image}
                  alt="Main"
                  className="w-full h-full object-cover"
                />
              </div>
              {targetProduct.subImages.map((img, i) => (
                <div
                  key={i}
                  onClick={() => setCurrentImage(img)}
                  className={`w-16 h-16 rounded-xl cursor-pointer overflow-hidden transition-all duration-300 ${
                    currentImage === img
                      ? "ring-2 ring-black shadow-lg"
                      : "hover:shadow-md"
                  }`}
                >
                  <img
                    src={img}
                    alt={`View ${i + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Side - Product Details */}
        <div className="lg:w-1/2 p-8 lg:p-12 flex items-center">
          <div className="max-w-lg w-full">
            {/* Product Title */}
            <div className="mb-8">
              <div className="flex items-center gap-3 mb-4">
                <Badge className="bg-green-100 text-green-800 px-3 py-1 text-xs rounded-full">
                  IN STOCK
                </Badge>
                <span className="text-sm text-gray-500">
                  SKU: {targetProduct.id.toString().padStart(6, "0")}
                </span>
              </div>

              <h1 className="text-4xl lg:text-5xl font-light mb-4 text-gray-900 leading-tight">
                {targetProduct.name}
              </h1>

              <div className="flex items-center gap-6 mb-6">
                <div className="flex items-center">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400" />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 ml-3">
                    4.8 (23 Reviews)
                  </span>
                </div>

                <div className="flex items-center text-gray-600 text-sm">
                  <Eye className="w-4 h-4 mr-2" />
                  <span>{viewersCount} viewing</span>
                </div>
              </div>
            </div>

            {/* Price */}
            <div className="mb-8">
              <div className="flex items-baseline gap-4 mb-4">
                <span className="text-4xl font-light text-gray-900">
                  ${targetProduct.price}.00
                </span>
                <span className="text-xl text-gray-400 line-through">
                  ${(targetProduct.price * 1.2).toFixed(2)}
                </span>
              </div>

              <p className="text-gray-600 leading-relaxed">
                {targetProduct.description.substring(0, 200)}...
              </p>
            </div>

            {/* Size Selector */}
            {targetProduct.sizes && targetProduct.sizes.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Size</h3>
                <div className="flex gap-2">
                  {targetProduct.sizes.map((size) => (
                    <button
                      key={size}
                      className={`px-6 py-3 border rounded-xl text-sm transition-all duration-300 ${
                        selectedSize === size
                          ? "border-black bg-black text-white"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setSelectedSize(size)}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Color Selector */}
            {targetProduct.colors && targetProduct.colors.length > 0 && (
              <div className="mb-8">
                <h3 className="text-sm font-medium text-gray-900 mb-3">
                  Color
                </h3>
                <div className="flex gap-2">
                  {targetProduct.colors.map((color) => (
                    <button
                      key={color}
                      className={`px-6 py-3 border rounded-xl text-sm transition-all duration-300 ${
                        selectedColor === color
                          ? "border-black bg-black text-white"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setSelectedColor(color)}
                    >
                      {color}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity and Actions */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center border border-gray-200 rounded-xl">
                  <button
                    className="w-12 h-12 flex items-center justify-center hover:bg-gray-50 rounded-l-xl"
                    onClick={() => cartCount > 1 && setCartCount(cartCount - 1)}
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="w-16 h-12 flex items-center justify-center text-sm font-medium">
                    {cartCount}
                  </span>
                  <button
                    className="w-12 h-12 flex items-center justify-center hover:bg-gray-50 rounded-r-xl"
                    onClick={() => setCartCount(cartCount + 1)}
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>

                <Button
                  variant="outline"
                  size="lg"
                  className="rounded-xl border-gray-200 hover:border-gray-300"
                  onClick={async () => {
                    try {
                      const result = await addToWishlist({
                        productId: targetProduct.id,
                        name: targetProduct.name,
                        price: targetProduct.price,
                        image: targetProduct.image,
                      });

                      if (result.success) {
                        toast({
                          title: "Added to wishlist",
                          description: `${targetProduct.name} has been added to your wishlist`,
                        });
                      }
                    } catch (error) {
                      toast({
                        title: "Error",
                        description: "Failed to add product to wishlist",
                        variant: "destructive",
                      });
                    }
                  }}
                >
                  <Heart className="w-5 h-5" />
                </Button>
              </div>

              <AddToCartButton
                product={targetProduct}
                quantity={cartCount}
                selectedSize={selectedSize}
                selectedColor={selectedColor}
              />
            </div>

            {/* Features */}
            <div className="mt-8 pt-8 border-t border-gray-100">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2 text-gray-600">
                  <Truck className="w-4 h-4" />
                  <span>Free shipping</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <RotateCcw className="w-4 h-4" />
                  <span>30-day returns</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Shield className="w-4 h-4" />
                  <span>Secure checkout</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Award className="w-4 h-4" />
                  <span>Authentic products</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 3 - Magazine-Style Layout with Large Typography
function ProductVariant3({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="bg-white">
      {/* Hero Section with Large Typography */}
      <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-6 py-16 max-w-7xl">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left - Typography */}
            <div>
              <div className="flex items-center gap-4 mb-6">
                <Badge className="bg-white/20 text-white px-4 py-2 text-sm font-medium rounded-full backdrop-blur-sm">
                  NEW ARRIVAL
                </Badge>
                <Badge className="bg-red-500 text-white px-4 py-2 text-sm font-medium rounded-full">
                  LIMITED EDITION
                </Badge>
              </div>

              <h1 className="text-6xl lg:text-7xl font-bold mb-6 leading-none">
                {targetProduct.name.split(" ").map((word, i) => (
                  <span
                    key={i}
                    className={i % 2 === 0 ? "text-white" : "text-gray-300"}
                  >
                    {word}{" "}
                  </span>
                ))}
              </h1>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                {targetProduct.description.substring(0, 180)}...
              </p>

              <div className="flex items-center gap-8 mb-8">
                <div className="flex items-center">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-6 h-6 fill-yellow-400" />
                    ))}
                  </div>
                  <span className="text-gray-300 ml-3">4.8 (23 Reviews)</span>
                </div>

                <div className="flex items-center text-gray-300">
                  <Eye className="w-5 h-5 mr-2" />
                  <span>{viewersCount} viewing now</span>
                </div>
              </div>

              <div className="flex items-baseline gap-6">
                <span className="text-5xl font-bold text-white">
                  ${targetProduct.price}.00
                </span>
                <span className="text-2xl text-gray-400 line-through">
                  ${(targetProduct.price * 1.2).toFixed(2)}
                </span>
                <Badge className="bg-red-500 text-white px-3 py-1 text-sm font-medium rounded-full">
                  SAVE 20%
                </Badge>
              </div>
            </div>

            {/* Right - Image */}
            <div className="relative">
              <div className="aspect-square bg-white/10 rounded-3xl overflow-hidden backdrop-blur-sm">
                <img
                  src={currentImage}
                  alt={targetProduct.name}
                  className="w-full h-full object-contain p-8"
                />
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full opacity-80 blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full opacity-60 blur-xl"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Section */}
      <div className="container mx-auto px-6 py-16 max-w-7xl">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Left - Image Gallery */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="grid grid-cols-2 gap-4">
                <div
                  onClick={() => setCurrentImage(targetProduct.image)}
                  className={`aspect-square rounded-2xl cursor-pointer overflow-hidden transition-all duration-300 ${
                    currentImage === targetProduct.image
                      ? "ring-4 ring-black shadow-xl"
                      : "hover:shadow-lg"
                  }`}
                >
                  <img
                    src={targetProduct.image}
                    alt="Main"
                    className="w-full h-full object-cover"
                  />
                </div>
                {targetProduct.subImages.slice(0, 3).map((img, i) => (
                  <div
                    key={i}
                    onClick={() => setCurrentImage(img)}
                    className={`aspect-square rounded-2xl cursor-pointer overflow-hidden transition-all duration-300 ${
                      currentImage === img
                        ? "ring-4 ring-black shadow-xl"
                        : "hover:shadow-lg"
                    }`}
                  >
                    <img
                      src={img}
                      alt={`View ${i + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Middle - Product Info */}
          <div className="lg:col-span-1">
            <div className="space-y-8">
              {/* Certifications */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Certifications</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2 p-3 bg-green-50 rounded-xl">
                    <Award className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-800">
                      Dermatologically Tested
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-xl">
                    <Award className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      Paraben Free
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-xl">
                    <Award className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-800">
                      Cruelty Free
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-3 bg-amber-50 rounded-xl">
                    <Leaf className="w-5 h-5 text-amber-600" />
                    <span className="text-sm font-medium text-amber-800">
                      Natural
                    </span>
                  </div>
                </div>
              </div>

              {/* Size Selector */}
              {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Size</h3>
                  <div className="grid grid-cols-3 gap-3">
                    {targetProduct.sizes.map((size) => (
                      <button
                        key={size}
                        className={`py-4 border-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                          selectedSize === size
                            ? "border-black bg-black text-white"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => setSelectedSize(size)}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Color Selector */}
              {targetProduct.colors && targetProduct.colors.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Color</h3>
                  <div className="grid grid-cols-2 gap-3">
                    {targetProduct.colors.map((color) => (
                      <button
                        key={color}
                        className={`py-4 border-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                          selectedColor === color
                            ? "border-black bg-black text-white"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => setSelectedColor(color)}
                      >
                        {color}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Features */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Features</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <Truck className="w-5 h-5 text-gray-600" />
                    <span className="text-sm">
                      Free shipping on orders over $100
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <RotateCcw className="w-5 h-5 text-gray-600" />
                    <span className="text-sm">30-day money-back guarantee</span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <Shield className="w-5 h-5 text-gray-600" />
                    <span className="text-sm">Secure SSL checkout</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right - Purchase */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 bg-gray-50 rounded-3xl p-8">
              <div className="space-y-6">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className="bg-green-100 text-green-800 px-3 py-1 text-xs rounded-full">
                      IN STOCK
                    </Badge>
                    <span className="text-sm text-gray-500">
                      SKU: {targetProduct.id.toString().padStart(6, "0")}
                    </span>
                  </div>
                  <h2 className="text-2xl font-bold mb-4">
                    {targetProduct.name}
                  </h2>
                </div>

                {/* Quantity */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-3">
                    Quantity
                  </h3>
                  <div className="flex items-center border-2 border-gray-200 rounded-xl">
                    <button
                      className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-l-xl"
                      onClick={() =>
                        cartCount > 1 && setCartCount(cartCount - 1)
                      }
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="flex-1 h-12 flex items-center justify-center text-lg font-medium">
                      {cartCount}
                    </span>
                    <button
                      className="w-12 h-12 flex items-center justify-center hover:bg-gray-100 rounded-r-xl"
                      onClick={() => setCartCount(cartCount + 1)}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <AddToCartButton
                    product={targetProduct}
                    quantity={cartCount}
                    selectedSize={selectedSize}
                    selectedColor={selectedColor}
                  />

                  <Button
                    variant="outline"
                    className="w-full py-3 rounded-xl border-2"
                    onClick={async () => {
                      try {
                        const result = await addToWishlist({
                          productId: targetProduct.id,
                          name: targetProduct.name,
                          price: targetProduct.price,
                          image: targetProduct.image,
                        });

                        if (result.success) {
                          toast({
                            title: "Added to wishlist",
                            description: `${targetProduct.name} has been added to your wishlist`,
                          });
                        }
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "Failed to add product to wishlist",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Heart className="w-5 h-5 mr-2" />
                    Add to Wishlist
                  </Button>
                </div>

                {/* Social Share */}
                <div className="pt-6 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">
                    Share
                  </h3>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-xl"
                    >
                      <Facebook className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-xl"
                    >
                      <Twitter className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-xl"
                    >
                      <Instagram className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-xl"
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 4 - Card-Based Modular Design
function ProductVariant4({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
      {/* Floating Breadcrumbs */}
      <div className="container mx-auto px-6 py-6 max-w-7xl">
        <div className="bg-white rounded-2xl shadow-sm p-4 mb-6">
          <div className="flex items-center text-sm text-gray-500">
            <Link href="/" className="hover:text-gray-700 transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link
              href="/products"
              className="hover:text-gray-700 transition-colors"
            >
              Products
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-gray-900 font-medium">
              {targetProduct.name}
            </span>
          </div>
        </div>

        {/* Modular Card Layout */}
        <div className="grid lg:grid-cols-12 gap-6">
          {/* Main Image Card */}
          <div className="lg:col-span-6">
            <div className="bg-white rounded-3xl shadow-lg overflow-hidden">
              <div className="relative aspect-square bg-gradient-to-br from-gray-50 to-gray-100">
                <img
                  src={currentImage}
                  alt={targetProduct.name}
                  className="w-full h-full object-contain p-8 transition-transform duration-500 hover:scale-105"
                />

                {/* Floating Action Buttons */}
                <div className="absolute top-6 right-6 flex flex-col gap-3">
                  <Button
                    size="icon"
                    className="rounded-full bg-white/90 text-gray-700 hover:bg-white shadow-lg backdrop-blur-sm"
                    onClick={async () => {
                      try {
                        const result = await addToWishlist({
                          productId: targetProduct.id,
                          name: targetProduct.name,
                          price: targetProduct.price,
                          image: targetProduct.image,
                        });

                        if (result.success) {
                          toast({
                            title: "Added to wishlist",
                            description: `${targetProduct.name} has been added to your wishlist`,
                          });
                        }
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "Failed to add product to wishlist",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Heart className="w-5 h-5" />
                  </Button>
                  <Button
                    size="icon"
                    className="rounded-full bg-white/90 text-gray-700 hover:bg-white shadow-lg backdrop-blur-sm"
                  >
                    <Share2 className="w-5 h-5" />
                  </Button>
                  <Button
                    size="icon"
                    className="rounded-full bg-white/90 text-gray-700 hover:bg-white shadow-lg backdrop-blur-sm"
                  >
                    <Maximize2 className="w-5 h-5" />
                  </Button>
                </div>

                {/* Status Badges */}
                <div className="absolute top-6 left-6 flex flex-col gap-2">
                  <Badge className="bg-green-500 text-white px-3 py-1 text-sm font-medium rounded-full shadow-lg">
                    IN STOCK
                  </Badge>
                  <Badge className="bg-red-500 text-white px-3 py-1 text-sm font-medium rounded-full shadow-lg">
                    -20% OFF
                  </Badge>
                </div>
              </div>

              {/* Thumbnail Strip */}
              <div className="p-6">
                <div className="flex gap-3 overflow-x-auto">
                  <div
                    onClick={() => setCurrentImage(targetProduct.image)}
                    className={`flex-shrink-0 w-20 h-20 rounded-xl cursor-pointer overflow-hidden transition-all duration-300 ${
                      currentImage === targetProduct.image
                        ? "ring-2 ring-blue-500 shadow-lg"
                        : "hover:shadow-md"
                    }`}
                  >
                    <img
                      src={targetProduct.image}
                      alt="Main"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {targetProduct.subImages.map((img, i) => (
                    <div
                      key={i}
                      onClick={() => setCurrentImage(img)}
                      className={`flex-shrink-0 w-20 h-20 rounded-xl cursor-pointer overflow-hidden transition-all duration-300 ${
                        currentImage === img
                          ? "ring-2 ring-blue-500 shadow-lg"
                          : "hover:shadow-md"
                      }`}
                    >
                      <img
                        src={img}
                        alt={`View ${i + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Product Info Cards */}
          <div className="lg:col-span-6 space-y-6">
            {/* Title & Rating Card */}
            <div className="bg-white rounded-3xl shadow-lg p-8">
              <div className="flex items-center gap-3 mb-4">
                <span className="text-sm text-gray-500">
                  SKU: {targetProduct.id.toString().padStart(6, "0")}
                </span>
                <div className="flex items-center text-gray-600 text-sm">
                  <Eye className="w-4 h-4 mr-1" />
                  <span>{viewersCount} viewing</span>
                </div>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-900">
                {targetProduct.name}
              </h1>

              <div className="flex items-center gap-6 mb-6">
                <div className="flex items-center">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400" />
                    ))}
                  </div>
                  <span className="text-gray-600 ml-2">4.8 (23 Reviews)</span>
                </div>
              </div>

              <p className="text-gray-600 leading-relaxed">
                {targetProduct.description.substring(0, 200)}...
              </p>
            </div>

            {/* Price Card */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl shadow-lg p-8 text-white">
              <div className="flex items-baseline gap-4 mb-4">
                <span className="text-4xl font-bold">
                  ${targetProduct.price}.00
                </span>
                <span className="text-xl text-blue-100 line-through">
                  ${(targetProduct.price * 1.2).toFixed(2)}
                </span>
              </div>

              <div className="bg-white/20 rounded-2xl p-4 backdrop-blur-sm">
                <div className="flex items-center gap-2 text-white/90 mb-2">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Flash Sale Ends In:
                  </span>
                </div>
                <div className="flex gap-2">
                  <div className="bg-white/30 rounded-lg px-3 py-2 text-center">
                    <div className="text-lg font-bold">
                      {formatTime(timeLeft).split(":")[0]}
                    </div>
                    <div className="text-xs">Hours</div>
                  </div>
                  <div className="bg-white/30 rounded-lg px-3 py-2 text-center">
                    <div className="text-lg font-bold">
                      {formatTime(timeLeft).split(":")[1]}
                    </div>
                    <div className="text-xs">Mins</div>
                  </div>
                  <div className="bg-white/30 rounded-lg px-3 py-2 text-center">
                    <div className="text-lg font-bold">
                      {formatTime(timeLeft).split(":")[2]}
                    </div>
                    <div className="text-xs">Secs</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Options Card */}
            <div className="bg-white rounded-3xl shadow-lg p-8">
              <div className="space-y-6">
                {/* Size Selector */}
                {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Size</h3>
                    <div className="flex flex-wrap gap-3">
                      {targetProduct.sizes.map((size) => (
                        <button
                          key={size}
                          className={`px-6 py-3 border-2 rounded-2xl text-sm font-medium transition-all duration-300 ${
                            selectedSize === size
                              ? "border-blue-500 bg-blue-500 text-white"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selector */}
                {targetProduct.colors && targetProduct.colors.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Color</h3>
                    <div className="flex flex-wrap gap-3">
                      {targetProduct.colors.map((color) => (
                        <button
                          key={color}
                          className={`px-6 py-3 border-2 rounded-2xl text-sm font-medium transition-all duration-300 ${
                            selectedColor === color
                              ? "border-blue-500 bg-blue-500 text-white"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => setSelectedColor(color)}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Quantity</h3>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center border-2 border-gray-200 rounded-2xl">
                      <button
                        className="w-12 h-12 flex items-center justify-center hover:bg-gray-50 rounded-l-2xl"
                        onClick={() =>
                          cartCount > 1 && setCartCount(cartCount - 1)
                        }
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="w-16 h-12 flex items-center justify-center text-lg font-medium">
                        {cartCount}
                      </span>
                      <button
                        className="w-12 h-12 flex items-center justify-center hover:bg-gray-50 rounded-r-2xl"
                        onClick={() => setCartCount(cartCount + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Card */}
            <div className="bg-white rounded-3xl shadow-lg p-8">
              <div className="space-y-4">
                <AddToCartButton
                  product={targetProduct}
                  quantity={cartCount}
                  selectedSize={selectedSize}
                  selectedColor={selectedColor}
                />

                <Button
                  variant="outline"
                  className="w-full py-4 rounded-2xl border-2 text-lg"
                  onClick={() => {
                    const addToCartBtn =
                      document.querySelector("[data-add-to-cart]");
                    if (addToCartBtn) {
                      addToCartBtn.click();
                      setTimeout(() => {
                        window.location.href = "/checkout";
                      }, 500);
                    }
                  }}
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Buy Now
                </Button>
              </div>
            </div>

            {/* Features Card */}
            <div className="bg-white rounded-3xl shadow-lg p-8">
              <h3 className="text-lg font-semibold mb-4">
                Why Choose This Product?
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-2xl">
                  <Truck className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Free Shipping
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-2xl">
                  <RotateCcw className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    30-Day Returns
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-2xl">
                  <Shield className="w-5 h-5 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800">
                    Secure Payment
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-amber-50 rounded-2xl">
                  <Award className="w-5 h-5 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">
                    Authentic
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 5 - Full-Width Immersive Layout
function ProductVariant5({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="bg-black text-white min-h-screen">
      {/* Immersive Hero Section */}
      <div className="relative h-screen overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            src={currentImage}
            alt={targetProduct.name}
            className="w-full h-full object-cover opacity-30"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black via-black/80 to-transparent"></div>
        </div>

        {/* Floating Navigation */}
        <div className="absolute top-6 left-6 right-6 z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-300">
              <Link href="/" className="hover:text-white transition-colors">
                Home
              </Link>
              <span className="mx-3">/</span>
              <Link
                href="/products"
                className="hover:text-white transition-colors"
              >
                Products
              </Link>
              <span className="mx-3">/</span>
              <span className="text-white">{targetProduct.name}</span>
            </div>

            <div className="flex items-center gap-3">
              <Button
                size="icon"
                variant="ghost"
                className="rounded-full bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm"
                onClick={async () => {
                  try {
                    const result = await addToWishlist({
                      productId: targetProduct.id,
                      name: targetProduct.name,
                      price: targetProduct.price,
                      image: targetProduct.image,
                    });

                    if (result.success) {
                      toast({
                        title: "Added to wishlist",
                        description: `${targetProduct.name} has been added to your wishlist`,
                      });
                    }
                  } catch (error) {
                    toast({
                      title: "Error",
                      description: "Failed to add product to wishlist",
                      variant: "destructive",
                    });
                  }
                }}
              >
                <Heart className="w-5 h-5" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="rounded-full bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <Share2 className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 h-full flex items-center">
          <div className="container mx-auto px-6 max-w-7xl">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left - Product Info */}
              <div className="space-y-8">
                <div className="flex items-center gap-4">
                  <Badge className="bg-white/20 text-white px-4 py-2 text-sm font-medium rounded-full backdrop-blur-sm">
                    PREMIUM COLLECTION
                  </Badge>
                  <Badge className="bg-red-500 text-white px-4 py-2 text-sm font-medium rounded-full">
                    LIMITED TIME
                  </Badge>
                </div>

                <div>
                  <h1 className="text-6xl lg:text-8xl font-bold mb-6 leading-none">
                    {targetProduct.name.split(" ").map((word, i) => (
                      <div key={i} className="overflow-hidden">
                        <span className="block transform transition-transform duration-1000 hover:translate-x-2">
                          {word}
                        </span>
                      </div>
                    ))}
                  </h1>

                  <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-lg">
                    {targetProduct.description.substring(0, 200)}...
                  </p>
                </div>

                <div className="flex items-center gap-8">
                  <div className="flex items-center">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-6 h-6 fill-yellow-400" />
                      ))}
                    </div>
                    <span className="text-gray-300 ml-3">4.8 (23 Reviews)</span>
                  </div>

                  <div className="flex items-center text-gray-300">
                    <Eye className="w-5 h-5 mr-2" />
                    <span>{viewersCount} viewing now</span>
                  </div>
                </div>

                <div className="flex items-baseline gap-6">
                  <span className="text-6xl font-bold text-white">
                    ${targetProduct.price}.00
                  </span>
                  <span className="text-2xl text-gray-400 line-through">
                    ${(targetProduct.price * 1.2).toFixed(2)}
                  </span>
                </div>

                {/* Quick Actions */}
                <div className="flex items-center gap-4">
                  <AddToCartButton
                    product={targetProduct}
                    quantity={cartCount}
                    selectedSize={selectedSize}
                    selectedColor={selectedColor}
                  />

                  <Button
                    size="lg"
                    className="bg-white text-black hover:bg-gray-100 px-8 py-4 rounded-full"
                    onClick={() => {
                      const addToCartBtn =
                        document.querySelector("[data-add-to-cart]");
                      if (addToCartBtn) {
                        addToCartBtn.click();
                        setTimeout(() => {
                          window.location.href = "/checkout";
                        }, 500);
                      }
                    }}
                  >
                    <Zap className="w-5 h-5 mr-2" />
                    Buy Now
                  </Button>
                </div>
              </div>

              {/* Right - Product Showcase */}
              <div className="relative">
                <div className="aspect-square bg-white/5 rounded-full overflow-hidden backdrop-blur-sm border border-white/10">
                  <img
                    src={currentImage}
                    alt={targetProduct.name}
                    className="w-full h-full object-contain p-12 transition-transform duration-700 hover:scale-110"
                  />
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-8 -right-8 w-32 h-32 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full opacity-60 blur-2xl animate-pulse"></div>
                <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full opacity-40 blur-2xl animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Navigation */}
        <div className="absolute bottom-6 left-6 right-6 z-10">
          <div className="flex items-center justify-between">
            <div className="flex gap-3">
              <div
                onClick={() => setCurrentImage(targetProduct.image)}
                className={`w-16 h-16 rounded-2xl cursor-pointer overflow-hidden transition-all duration-300 ${
                  currentImage === targetProduct.image
                    ? "ring-2 ring-white shadow-2xl"
                    : "opacity-60 hover:opacity-100"
                }`}
              >
                <img
                  src={targetProduct.image}
                  alt="Main"
                  className="w-full h-full object-cover"
                />
              </div>
              {targetProduct.subImages.slice(0, 4).map((img, i) => (
                <div
                  key={i}
                  onClick={() => setCurrentImage(img)}
                  className={`w-16 h-16 rounded-2xl cursor-pointer overflow-hidden transition-all duration-300 ${
                    currentImage === img
                      ? "ring-2 ring-white shadow-2xl"
                      : "opacity-60 hover:opacity-100"
                  }`}
                >
                  <img
                    src={img}
                    alt={`View ${i + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>

            <div className="flex items-center gap-6">
              {/* Size Selector */}
              {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-300">Size:</span>
                  <div className="flex gap-2">
                    {targetProduct.sizes.map((size) => (
                      <button
                        key={size}
                        className={`px-4 py-2 rounded-full text-sm transition-all duration-300 ${
                          selectedSize === size
                            ? "bg-white text-black"
                            : "bg-white/20 text-white hover:bg-white/30"
                        }`}
                        onClick={() => setSelectedSize(size)}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Color Selector */}
              {targetProduct.colors && targetProduct.colors.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-300">Color:</span>
                  <div className="flex gap-2">
                    {targetProduct.colors.map((color) => (
                      <button
                        key={color}
                        className={`px-4 py-2 rounded-full text-sm transition-all duration-300 ${
                          selectedColor === color
                            ? "bg-white text-black"
                            : "bg-white/20 text-white hover:bg-white/30"
                        }`}
                        onClick={() => setSelectedColor(color)}
                      >
                        {color}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Quantity */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-300">Qty:</span>
                <div className="flex items-center bg-white/20 rounded-full backdrop-blur-sm">
                  <button
                    className="w-10 h-10 flex items-center justify-center hover:bg-white/20 rounded-l-full"
                    onClick={() => cartCount > 1 && setCartCount(cartCount - 1)}
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="w-12 h-10 flex items-center justify-center text-sm font-medium">
                    {cartCount}
                  </span>
                  <button
                    className="w-10 h-10 flex items-center justify-center hover:bg-white/20 rounded-r-full"
                    onClick={() => setCartCount(cartCount + 1)}
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gray-900 py-16">
        <div className="container mx-auto px-6 max-w-7xl">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Truck className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Free Shipping</h3>
              <p className="text-gray-400 text-sm">On orders over $100</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <RotateCcw className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Easy Returns</h3>
              <p className="text-gray-400 text-sm">30-day return policy</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Secure Payment</h3>
              <p className="text-gray-400 text-sm">SSL encrypted checkout</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Authentic</h3>
              <p className="text-gray-400 text-sm">100% genuine products</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 6 - Modern Glassmorphism Design
function ProductVariant6({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/15 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300/20 rounded-full blur-2xl animate-bounce"></div>
        <div className="absolute bottom-20 left-20 w-48 h-48 bg-blue-300/20 rounded-full blur-2xl animate-pulse"></div>
      </div>

      {/* Glassmorphism Navigation */}
      <div className="relative z-10 backdrop-blur-md bg-white/10 border-b border-white/20">
        <div className="container mx-auto px-6 py-4 max-w-7xl">
          <div className="flex items-center text-sm text-white/80">
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link
              href="/products"
              className="hover:text-white transition-colors"
            >
              Products
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-white font-medium">{targetProduct.name}</span>
          </div>
        </div>
      </div>

      {/* Main Product Section */}
      <div className="relative z-10 container mx-auto px-6 py-12 max-w-7xl">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left - Product Images */}
          <div className="space-y-6">
            {/* Main Image Card */}
            <div className="backdrop-blur-xl bg-white/20 rounded-3xl p-8 border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-300">
              <div className="relative aspect-square bg-white/10 rounded-2xl overflow-hidden group">
                <img
                  src={currentImage}
                  alt={targetProduct.name}
                  className="w-full h-full object-contain p-6 transition-transform duration-500 group-hover:scale-105"
                />

                {/* Floating Badges */}
                <div className="absolute top-4 left-4 flex flex-col gap-2">
                  <Badge className="bg-white/30 text-white px-3 py-1 text-sm font-medium rounded-full backdrop-blur-sm border border-white/40">
                    PREMIUM
                  </Badge>
                  <Badge className="bg-red-500/80 text-white px-3 py-1 text-sm font-medium rounded-full backdrop-blur-sm">
                    -20% OFF
                  </Badge>
                </div>

                {/* Action Buttons */}
                <div className="absolute top-4 right-4 flex flex-col gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="backdrop-blur-sm bg-white/20 text-white hover:bg-white/30 rounded-full border border-white/30"
                    onClick={async () => {
                      try {
                        const result = await addToWishlist({
                          productId: targetProduct.id,
                          name: targetProduct.name,
                          price: targetProduct.price,
                          image: targetProduct.image,
                        });
                        if (result.success) {
                          toast({
                            title: "Added to wishlist",
                            description: `${targetProduct.name} has been added to your wishlist`,
                          });
                        }
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "Failed to add product to wishlist",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="backdrop-blur-sm bg-white/20 text-white hover:bg-white/30 rounded-full border border-white/30"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Thumbnail Gallery */}
            <div className="backdrop-blur-xl bg-white/20 rounded-2xl p-4 border border-white/30">
              <div className="grid grid-cols-5 gap-3">
                <div
                  onClick={() => setCurrentImage(targetProduct.image)}
                  className={`aspect-square rounded-xl overflow-hidden cursor-pointer border-2 transition-all ${
                    currentImage === targetProduct.image
                      ? "border-white shadow-lg"
                      : "border-white/30 hover:border-white/60"
                  }`}
                >
                  <img
                    src={targetProduct.image}
                    alt="Main"
                    className="w-full h-full object-cover"
                  />
                </div>
                {targetProduct.subImages.map((img, i) => (
                  <div
                    key={i}
                    onClick={() => setCurrentImage(img)}
                    className={`aspect-square rounded-xl overflow-hidden cursor-pointer border-2 transition-all ${
                      currentImage === img
                        ? "border-white shadow-lg"
                        : "border-white/30 hover:border-white/60"
                    }`}
                  >
                    <img
                      src={img}
                      alt={`View ${i + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right - Product Details */}
          <div className="space-y-6">
            {/* Title & Rating Card */}
            <div className="backdrop-blur-xl bg-white/20 rounded-3xl p-8 border border-white/30 shadow-2xl">
              <div className="flex items-center gap-3 mb-4">
                <Badge className="bg-green-500/80 text-white px-3 py-1 text-xs rounded-full backdrop-blur-sm">
                  IN STOCK
                </Badge>
                <span className="text-sm text-white/80">
                  SKU: {targetProduct.id.toString().padStart(6, "0")}
                </span>
                <div className="flex items-center text-white/80 text-sm">
                  <Eye className="w-4 h-4 mr-1" />
                  <span>{viewersCount} viewing</span>
                </div>
              </div>

              <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-white leading-tight">
                {targetProduct.name}
              </h1>

              <div className="flex items-center gap-6 mb-6">
                <div className="flex items-center">
                  <div className="flex text-yellow-300">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-300" />
                    ))}
                  </div>
                  <span className="text-white/90 ml-2">4.8 (23 Reviews)</span>
                </div>
              </div>

              <p className="text-white/80 leading-relaxed">
                {targetProduct.description.substring(0, 200)}...
              </p>
            </div>

            {/* Price Card */}
            <div className="backdrop-blur-xl bg-white/20 rounded-3xl p-8 border border-white/30 shadow-2xl">
              <div className="flex items-baseline gap-4 mb-6">
                <span className="text-5xl font-bold text-white">
                  ${targetProduct.price}.00
                </span>
                <span className="text-2xl text-white/60 line-through">
                  ${(targetProduct.price * 1.2).toFixed(2)}
                </span>
              </div>

              {/* Countdown Timer */}
              <div className="bg-white/10 rounded-2xl p-4 backdrop-blur-sm border border-white/20">
                <div className="flex items-center gap-2 text-white/90 mb-3">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Flash Sale Ends In:
                  </span>
                </div>
                <div className="flex gap-2">
                  <div className="bg-white/20 rounded-lg px-3 py-2 text-center backdrop-blur-sm">
                    <div className="text-lg font-bold text-white">
                      {formatTime(timeLeft).split(":")[0]}
                    </div>
                    <div className="text-xs text-white/80">Hours</div>
                  </div>
                  <div className="bg-white/20 rounded-lg px-3 py-2 text-center backdrop-blur-sm">
                    <div className="text-lg font-bold text-white">
                      {formatTime(timeLeft).split(":")[1]}
                    </div>
                    <div className="text-xs text-white/80">Mins</div>
                  </div>
                  <div className="bg-white/20 rounded-lg px-3 py-2 text-center backdrop-blur-sm">
                    <div className="text-lg font-bold text-white">
                      {formatTime(timeLeft).split(":")[2]}
                    </div>
                    <div className="text-xs text-white/80">Secs</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Options Card */}
            <div className="backdrop-blur-xl bg-white/20 rounded-3xl p-8 border border-white/30 shadow-2xl">
              <div className="space-y-6">
                {/* Size Selector */}
                {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-white">
                      Size
                    </h3>
                    <div className="flex flex-wrap gap-3">
                      {targetProduct.sizes.map((size) => (
                        <button
                          key={size}
                          className={`px-6 py-3 rounded-2xl text-sm font-medium transition-all duration-300 backdrop-blur-sm border ${
                            selectedSize === size
                              ? "bg-white text-purple-600 border-white"
                              : "bg-white/20 text-white border-white/30 hover:bg-white/30"
                          }`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selector */}
                {targetProduct.colors && targetProduct.colors.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-white">
                      Color
                    </h3>
                    <div className="flex flex-wrap gap-3">
                      {targetProduct.colors.map((color) => (
                        <button
                          key={color}
                          className={`px-6 py-3 rounded-2xl text-sm font-medium transition-all duration-300 backdrop-blur-sm border ${
                            selectedColor === color
                              ? "bg-white text-purple-600 border-white"
                              : "bg-white/20 text-white border-white/30 hover:bg-white/30"
                          }`}
                          onClick={() => setSelectedColor(color)}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 text-white">
                    Quantity
                  </h3>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center bg-white/20 rounded-2xl backdrop-blur-sm border border-white/30">
                      <button
                        className="w-12 h-12 flex items-center justify-center hover:bg-white/20 rounded-l-2xl text-white"
                        onClick={() =>
                          cartCount > 1 && setCartCount(cartCount - 1)
                        }
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="w-16 h-12 flex items-center justify-center text-lg font-medium text-white">
                        {cartCount}
                      </span>
                      <button
                        className="w-12 h-12 flex items-center justify-center hover:bg-white/20 rounded-r-2xl text-white"
                        onClick={() => setCartCount(cartCount + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="backdrop-blur-xl bg-white/20 rounded-3xl p-8 border border-white/30 shadow-2xl">
              <div className="space-y-4">
                <AddToCartButton
                  product={targetProduct}
                  quantity={cartCount}
                  selectedSize={selectedSize}
                  selectedColor={selectedColor}
                />

                <Button
                  className="w-full py-4 bg-white text-purple-600 hover:bg-white/90 rounded-2xl text-lg font-semibold"
                  onClick={() => {
                    const addToCartBtn =
                      document.querySelector("[data-add-to-cart]");
                    if (addToCartBtn) {
                      addToCartBtn.click();
                      setTimeout(() => {
                        window.location.href = "/checkout";
                      }, 500);
                    }
                  }}
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Buy Now
                </Button>
              </div>
            </div>

            {/* Features */}
            <div className="backdrop-blur-xl bg-white/20 rounded-3xl p-8 border border-white/30 shadow-2xl">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-2xl backdrop-blur-sm">
                  <Truck className="w-5 h-5 text-white" />
                  <span className="text-sm font-medium text-white">
                    Free Shipping
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-2xl backdrop-blur-sm">
                  <RotateCcw className="w-5 h-5 text-white" />
                  <span className="text-sm font-medium text-white">
                    30-Day Returns
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-2xl backdrop-blur-sm">
                  <Shield className="w-5 h-5 text-white" />
                  <span className="text-sm font-medium text-white">
                    Secure Payment
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-2xl backdrop-blur-sm">
                  <Award className="w-5 h-5 text-white" />
                  <span className="text-sm font-medium text-white">
                    Authentic
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 7 - Neon Cyberpunk Design
function ProductVariant7({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      {/* Animated Grid Background */}
      <div className="absolute inset-0 opacity-20">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
            backgroundSize: "50px 50px",
          }}
        ></div>
      </div>

      {/* Neon Glow Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-64 h-64 bg-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse"></div>
      </div>

      {/* Cyberpunk Navigation */}
      <div className="relative z-10 border-b border-cyan-500/30 bg-black/80 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4 max-w-7xl">
          <div className="flex items-center text-sm">
            <Link
              href="/"
              className="text-cyan-400 hover:text-cyan-300 transition-colors font-mono"
            >
              &gt; HOME
            </Link>
            <span className="mx-3 text-cyan-500">/</span>
            <Link
              href="/products"
              className="text-cyan-400 hover:text-cyan-300 transition-colors font-mono"
            >
              &gt; PRODUCTS
            </Link>
            <span className="mx-3 text-cyan-500">/</span>
            <span className="text-white font-mono">
              &gt; {targetProduct.name.toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-6 py-12 max-w-7xl">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left - Product Showcase */}
          <div className="space-y-6">
            {/* Main Product Display */}
            <div className="relative bg-gray-900/50 rounded-lg border border-cyan-500/30 p-8 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-purple-500/5 rounded-lg"></div>

              <div className="relative aspect-square bg-black/30 rounded-lg overflow-hidden border border-cyan-500/20">
                <img
                  src={currentImage}
                  alt={targetProduct.name}
                  className="w-full h-full object-contain p-6 transition-all duration-500 hover:scale-105 filter drop-shadow-[0_0_20px_rgba(0,255,255,0.3)]"
                />

                {/* Holographic Overlay */}
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-cyan-500/10 to-transparent opacity-50"></div>

                {/* Status Indicators */}
                <div className="absolute top-4 left-4 space-y-2">
                  <div className="flex items-center gap-2 bg-black/80 px-3 py-1 rounded border border-green-500/50">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-xs font-mono">
                      ONLINE
                    </span>
                  </div>
                  <div className="flex items-center gap-2 bg-black/80 px-3 py-1 rounded border border-red-500/50">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-red-400 text-xs font-mono">
                      LIMITED
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="absolute top-4 right-4 space-y-2">
                  <Button
                    size="icon"
                    className="bg-black/80 border border-cyan-500/50 text-cyan-400 hover:bg-cyan-500/20 hover:text-cyan-300"
                    onClick={async () => {
                      try {
                        const result = await addToWishlist({
                          productId: targetProduct.id,
                          name: targetProduct.name,
                          price: targetProduct.price,
                          image: targetProduct.image,
                        });

                        if (result.success) {
                          toast({
                            title: "Added to wishlist",
                            description: `${targetProduct.name} has been added to your wishlist`,
                          });
                        }
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "Failed to add product to wishlist",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Heart className="w-4 h-4" />
                  </Button>
                  <Button
                    size="icon"
                    className="bg-black/80 border border-purple-500/50 text-purple-400 hover:bg-purple-500/20 hover:text-purple-300"
                  >
                    <Share2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Image Selector */}
            <div className="bg-gray-900/50 rounded-lg border border-cyan-500/30 p-4 backdrop-blur-sm">
              <div className="flex gap-3 overflow-x-auto">
                <div
                  onClick={() => setCurrentImage(targetProduct.image)}
                  className={`flex-shrink-0 w-16 h-16 rounded border cursor-pointer overflow-hidden transition-all duration-300 ${
                    currentImage === targetProduct.image
                      ? "border-cyan-500 shadow-[0_0_10px_rgba(0,255,255,0.5)]"
                      : "border-gray-600 hover:border-cyan-400"
                  }`}
                >
                  <img
                    src={targetProduct.image}
                    alt="Main"
                    className="w-full h-full object-cover"
                  />
                </div>
                {targetProduct.subImages.map((img, i) => (
                  <div
                    key={i}
                    onClick={() => setCurrentImage(img)}
                    className={`flex-shrink-0 w-16 h-16 rounded border cursor-pointer overflow-hidden transition-all duration-300 ${
                      currentImage === img
                        ? "border-cyan-500 shadow-[0_0_10px_rgba(0,255,255,0.5)]"
                        : "border-gray-600 hover:border-cyan-400"
                    }`}
                  >
                    <img
                      src={img}
                      alt={`View ${i + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right - Product Data */}
          <div className="space-y-6">
            {/* Product Header */}
            <div className="bg-gray-900/50 rounded-lg border border-cyan-500/30 p-6 backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-4">
                <span className="text-xs font-mono text-cyan-400">
                  ID: {targetProduct.id.toString().padStart(6, "0")}
                </span>
                <div className="flex items-center gap-1 text-xs font-mono text-purple-400">
                  <Eye className="w-3 h-3" />
                  <span>{viewersCount}</span>
                </div>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-4 text-white font-mono tracking-wider">
                {targetProduct.name.toUpperCase()}
              </h1>

              <div className="flex items-center gap-4 mb-4">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400" />
                  ))}
                </div>
                <span className="text-gray-400 text-sm font-mono">
                  4.8/5.0 (23 REVIEWS)
                </span>
              </div>

              <p className="text-gray-300 leading-relaxed font-mono text-sm">
                {targetProduct.description.substring(0, 150)}...
              </p>
            </div>

            {/* Price Module */}
            <div className="bg-gray-900/50 rounded-lg border border-purple-500/30 p-6 backdrop-blur-sm">
              <div className="flex items-baseline gap-4 mb-4">
                <span className="text-4xl font-bold text-white font-mono">
                  ${targetProduct.price}.00
                </span>
                <span className="text-xl text-gray-500 line-through font-mono">
                  ${(targetProduct.price * 1.2).toFixed(2)}
                </span>
                <span className="text-sm font-mono text-red-400 bg-red-500/20 px-2 py-1 rounded border border-red-500/30">
                  -20%
                </span>
              </div>

              {/* Countdown */}
              <div className="bg-black/50 rounded border border-red-500/30 p-4">
                <div className="flex items-center gap-2 text-red-400 mb-2">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm font-mono">SALE EXPIRES:</span>
                </div>
                <div className="flex gap-2">
                  <div className="bg-red-500/20 border border-red-500/50 rounded px-2 py-1 text-center">
                    <div className="text-lg font-bold text-red-400 font-mono">
                      {formatTime(timeLeft).split(":")[0]}
                    </div>
                    <div className="text-xs text-red-300 font-mono">HRS</div>
                  </div>
                  <div className="bg-red-500/20 border border-red-500/50 rounded px-2 py-1 text-center">
                    <div className="text-lg font-bold text-red-400 font-mono">
                      {formatTime(timeLeft).split(":")[1]}
                    </div>
                    <div className="text-xs text-red-300 font-mono">MIN</div>
                  </div>
                  <div className="bg-red-500/20 border border-red-500/50 rounded px-2 py-1 text-center">
                    <div className="text-lg font-bold text-red-400 font-mono">
                      {formatTime(timeLeft).split(":")[2]}
                    </div>
                    <div className="text-xs text-red-300 font-mono">SEC</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Configuration Panel */}
            <div className="bg-gray-900/50 rounded-lg border border-cyan-500/30 p-6 backdrop-blur-sm">
              <h3 className="text-lg font-bold mb-4 text-cyan-400 font-mono">
                CONFIGURATION
              </h3>

              <div className="space-y-4">
                {/* Size Selection */}
                {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                  <div>
                    <label className="block text-sm font-mono text-gray-400 mb-2">
                      SIZE:
                    </label>
                    <div className="flex gap-2">
                      {targetProduct.sizes.map((size) => (
                        <button
                          key={size}
                          className={`px-4 py-2 border rounded font-mono text-sm transition-all duration-300 ${
                            selectedSize === size
                              ? "border-cyan-500 bg-cyan-500/20 text-cyan-400 shadow-[0_0_10px_rgba(0,255,255,0.3)]"
                              : "border-gray-600 text-gray-400 hover:border-cyan-400 hover:text-cyan-400"
                          }`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selection */}
                {targetProduct.colors && targetProduct.colors.length > 0 && (
                  <div>
                    <label className="block text-sm font-mono text-gray-400 mb-2">
                      COLOR:
                    </label>
                    <div className="flex gap-2">
                      {targetProduct.colors.map((color) => (
                        <button
                          key={color}
                          className={`px-4 py-2 border rounded font-mono text-sm transition-all duration-300 ${
                            selectedColor === color
                              ? "border-purple-500 bg-purple-500/20 text-purple-400 shadow-[0_0_10px_rgba(128,0,128,0.3)]"
                              : "border-gray-600 text-gray-400 hover:border-purple-400 hover:text-purple-400"
                          }`}
                          onClick={() => setSelectedColor(color)}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity */}
                <div>
                  <label className="block text-sm font-mono text-gray-400 mb-2">
                    QUANTITY:
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center border border-gray-600 rounded">
                      <button
                        className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-cyan-400 hover:bg-cyan-500/10"
                        onClick={() =>
                          cartCount > 1 && setCartCount(cartCount - 1)
                        }
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="w-16 h-10 flex items-center justify-center text-white font-mono bg-black/50">
                        {cartCount}
                      </span>
                      <button
                        className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-cyan-400 hover:bg-cyan-500/10"
                        onClick={() => setCartCount(cartCount + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Panel */}
            <div className="bg-gray-900/50 rounded-lg border border-green-500/30 p-6 backdrop-blur-sm">
              <div className="space-y-3">
                <AddToCartButton
                  product={targetProduct}
                  quantity={cartCount}
                  selectedSize={selectedSize}
                  selectedColor={selectedColor}
                />

                <Button
                  className="w-full py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-mono font-bold border border-purple-500/50 shadow-[0_0_20px_rgba(128,0,128,0.3)]"
                  onClick={() => {
                    const addToCartBtn =
                      document.querySelector("[data-add-to-cart]");
                    if (addToCartBtn) {
                      addToCartBtn.click();
                      setTimeout(() => {
                        window.location.href = "/checkout";
                      }, 500);
                    }
                  }}
                >
                  <Zap className="w-5 h-5 mr-2" />
                  INSTANT BUY
                </Button>
              </div>
            </div>

            {/* System Status */}
            <div className="bg-gray-900/50 rounded-lg border border-cyan-500/30 p-6 backdrop-blur-sm">
              <h3 className="text-sm font-mono text-cyan-400 mb-3">
                SYSTEM STATUS:
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center gap-2 text-xs font-mono">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400">SHIPPING: ACTIVE</span>
                </div>
                <div className="flex items-center gap-2 text-xs font-mono">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400">RETURNS: ENABLED</span>
                </div>
                <div className="flex items-center gap-2 text-xs font-mono">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400">SECURITY: SSL</span>
                </div>
                <div className="flex items-center gap-2 text-xs font-mono">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400">AUTH: VERIFIED</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 8 - Luxury Boutique Design
function ProductVariant8({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-rose-50">
      {/* Elegant Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-amber-200/50">
        <div className="container mx-auto px-6 py-4 max-w-7xl">
          <div className="flex items-center text-sm text-amber-800/70">
            <Link
              href="/"
              className="hover:text-amber-900 transition-colors italic"
            >
              Home
            </Link>
            <span className="mx-3 text-amber-600">•</span>
            <Link
              href="/products"
              className="hover:text-amber-900 transition-colors italic"
            >
              Collection
            </Link>
            <span className="mx-3 text-amber-600">•</span>
            <span className="text-amber-900 font-medium">
              {targetProduct.name}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-16 max-w-7xl">
        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Left - Product Gallery */}
          <div className="space-y-8">
            {/* Main Image */}
            <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden border border-amber-100">
              <div className="aspect-square bg-gradient-to-br from-amber-50 to-rose-50 p-12">
                <img
                  src={currentImage}
                  alt={targetProduct.name}
                  className="w-full h-full object-contain transition-transform duration-700 hover:scale-105"
                />

                {/* Luxury Badges */}
                <div className="absolute top-6 left-6 space-y-3">
                  <div className="bg-gradient-to-r from-amber-600 to-amber-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                    ✨ Exclusive
                  </div>
                  <div className="bg-gradient-to-r from-rose-500 to-rose-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                    Limited Edition
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="absolute top-6 right-6 space-y-3">
                  <Button
                    size="icon"
                    className="rounded-full bg-white/90 text-amber-700 hover:bg-white shadow-xl border border-amber-200"
                    onClick={async () => {
                      try {
                        const result = await addToWishlist({
                          productId: targetProduct.id,
                          name: targetProduct.name,
                          price: targetProduct.price,
                          image: targetProduct.image,
                        });

                        if (result.success) {
                          toast({
                            title: "Added to wishlist",
                            description: `${targetProduct.name} has been added to your wishlist`,
                          });
                        }
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "Failed to add product to wishlist",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Heart className="w-5 h-5" />
                  </Button>
                  <Button
                    size="icon"
                    className="rounded-full bg-white/90 text-amber-700 hover:bg-white shadow-xl border border-amber-200"
                  >
                    <Share2 className="w-5 h-5" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Thumbnail Gallery */}
            <div className="flex gap-4 justify-center">
              <div
                onClick={() => setCurrentImage(targetProduct.image)}
                className={`w-20 h-20 rounded-2xl cursor-pointer overflow-hidden transition-all duration-300 border-2 ${
                  currentImage === targetProduct.image
                    ? "border-amber-500 shadow-lg scale-110"
                    : "border-amber-200 hover:border-amber-400"
                }`}
              >
                <img
                  src={targetProduct.image}
                  alt="Main"
                  className="w-full h-full object-cover"
                />
              </div>
              {targetProduct.subImages.map((img, i) => (
                <div
                  key={i}
                  onClick={() => setCurrentImage(img)}
                  className={`w-20 h-20 rounded-2xl cursor-pointer overflow-hidden transition-all duration-300 border-2 ${
                    currentImage === img
                      ? "border-amber-500 shadow-lg scale-110"
                      : "border-amber-200 hover:border-amber-400"
                  }`}
                >
                  <img
                    src={img}
                    alt={`View ${i + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Right - Product Details */}
          <div className="space-y-8">
            {/* Product Header */}
            <div className="text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start gap-4 mb-4">
                <div className="bg-green-100 text-green-800 px-3 py-1 text-xs rounded-full font-medium">
                  ✓ In Stock
                </div>
                <span className="text-sm text-amber-700/70">
                  SKU: {targetProduct.id.toString().padStart(6, "0")}
                </span>
                <div className="flex items-center text-amber-700/70 text-sm">
                  <Eye className="w-4 h-4 mr-1" />
                  <span>{viewersCount} viewing</span>
                </div>
              </div>

              <h1 className="text-4xl lg:text-5xl font-serif font-bold mb-6 text-amber-900 leading-tight">
                {targetProduct.name}
              </h1>

              <div className="flex items-center justify-center lg:justify-start gap-6 mb-6">
                <div className="flex items-center">
                  <div className="flex text-amber-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-amber-400" />
                    ))}
                  </div>
                  <span className="text-amber-700/80 ml-2 font-medium">
                    4.8 (23 Reviews)
                  </span>
                </div>
              </div>

              <p className="text-amber-800/80 leading-relaxed text-lg font-light">
                {targetProduct.description.substring(0, 200)}...
              </p>
            </div>

            {/* Price Section */}
            <div className="bg-gradient-to-r from-amber-50 to-rose-50 rounded-3xl p-8 border border-amber-200">
              <div className="text-center">
                <div className="flex items-baseline justify-center gap-4 mb-4">
                  <span className="text-5xl font-serif font-bold text-amber-900">
                    ${targetProduct.price}.00
                  </span>
                  <span className="text-2xl text-amber-600/60 line-through font-light">
                    ${(targetProduct.price * 1.2).toFixed(2)}
                  </span>
                </div>

                <div className="inline-flex items-center gap-2 bg-rose-100 text-rose-800 px-4 py-2 rounded-full text-sm font-medium">
                  <Sparkles className="w-4 h-4" />
                  <span>Save 20% - Limited Time</span>
                </div>
              </div>
            </div>

            {/* Product Options */}
            <div className="space-y-6">
              {/* Size Selection */}
              {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                <div>
                  <h3 className="text-lg font-serif font-semibold mb-4 text-amber-900">
                    Size
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {targetProduct.sizes.map((size) => (
                      <button
                        key={size}
                        className={`px-6 py-3 rounded-2xl text-sm font-medium transition-all duration-300 border-2 ${
                          selectedSize === size
                            ? "border-amber-500 bg-amber-500 text-white shadow-lg"
                            : "border-amber-200 text-amber-800 hover:border-amber-400 bg-white"
                        }`}
                        onClick={() => setSelectedSize(size)}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Color Selection */}
              {targetProduct.colors && targetProduct.colors.length > 0 && (
                <div>
                  <h3 className="text-lg font-serif font-semibold mb-4 text-amber-900">
                    Color
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {targetProduct.colors.map((color) => (
                      <button
                        key={color}
                        className={`px-6 py-3 rounded-2xl text-sm font-medium transition-all duration-300 border-2 ${
                          selectedColor === color
                            ? "border-rose-500 bg-rose-500 text-white shadow-lg"
                            : "border-amber-200 text-amber-800 hover:border-rose-400 bg-white"
                        }`}
                        onClick={() => setSelectedColor(color)}
                      >
                        {color}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Quantity */}
              <div>
                <h3 className="text-lg font-serif font-semibold mb-4 text-amber-900">
                  Quantity
                </h3>
                <div className="flex items-center justify-center lg:justify-start gap-4">
                  <div className="flex items-center border-2 border-amber-200 rounded-2xl bg-white">
                    <button
                      className="w-12 h-12 flex items-center justify-center text-amber-700 hover:bg-amber-50 rounded-l-2xl"
                      onClick={() =>
                        cartCount > 1 && setCartCount(cartCount - 1)
                      }
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-16 h-12 flex items-center justify-center text-lg font-semibold text-amber-900">
                      {cartCount}
                    </span>
                    <button
                      className="w-12 h-12 flex items-center justify-center text-amber-700 hover:bg-amber-50 rounded-r-2xl"
                      onClick={() => setCartCount(cartCount + 1)}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <AddToCartButton
                product={targetProduct}
                quantity={cartCount}
                selectedSize={selectedSize}
                selectedColor={selectedColor}
              />

              <Button
                className="w-full py-4 bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white rounded-2xl text-lg font-semibold shadow-xl"
                onClick={() => {
                  const addToCartBtn =
                    document.querySelector("[data-add-to-cart]");
                  if (addToCartBtn) {
                    addToCartBtn.click();
                    setTimeout(() => {
                      window.location.href = "/checkout";
                    }, 500);
                  }
                }}
              >
                <Zap className="w-5 h-5 mr-2" />
                Buy Now
              </Button>
            </div>

            {/* Luxury Features */}
            <div className="bg-white rounded-3xl p-8 border border-amber-200 shadow-lg">
              <h3 className="text-lg font-serif font-semibold mb-6 text-amber-900 text-center">
                Luxury Experience
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-amber-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <Truck className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-amber-900 mb-1">
                    Free Shipping
                  </h4>
                  <p className="text-sm text-amber-700/70">
                    Complimentary delivery
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-rose-400 to-rose-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <Gift className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-amber-900 mb-1">
                    Gift Wrapping
                  </h4>
                  <p className="text-sm text-amber-700/70">
                    Elegant presentation
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-amber-900 mb-1">
                    Authenticity
                  </h4>
                  <p className="text-sm text-amber-700/70">
                    Guaranteed genuine
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-amber-900 mb-1">
                    Concierge
                  </h4>
                  <p className="text-sm text-amber-700/70">Personal service</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 9 - Beautiful Mobile-First Design
function ProductVariant9({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50">
      {/* Mobile Container */}
      <div className="max-w-md mx-auto bg-white min-h-screen shadow-2xl">
        {/* Header */}
        <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100 px-4 py-3">
          <div className="flex items-center justify-between">
            <Link href="/products" className="p-2 -ml-2">
              <ChevronRight className="w-5 h-5 rotate-180 text-gray-600" />
            </Link>
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full"
                onClick={async () => {
                  try {
                    const result = await addToWishlist({
                      productId: targetProduct.id,
                      name: targetProduct.name,
                      price: targetProduct.price,
                      image: targetProduct.image,
                    });
                    if (result.success) {
                      toast({
                        title: "Added to wishlist",
                        description: `${targetProduct.name} has been added to your wishlist`,
                      });
                    }
                  } catch (error) {
                    toast({
                      title: "Error",
                      description: "Failed to add product to wishlist",
                      variant: "destructive",
                    });
                  }
                }}
              >
                <Heart className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="icon" className="rounded-full">
                <Share2 className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Product Image Section */}
        <div className="relative">
          <div className="aspect-square bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
            <img
              src={currentImage}
              alt={targetProduct.name}
              className="w-full h-full object-contain p-8 transition-transform duration-300"
            />

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              <Badge className="bg-black text-white px-3 py-1 text-xs rounded-full">
                NEW
              </Badge>
              <Badge className="bg-red-500 text-white px-3 py-1 text-xs rounded-full">
                -20%
              </Badge>
            </div>

            {/* Live Viewers */}
            <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-gray-700">
                {viewersCount} viewing
              </span>
            </div>
          </div>

          {/* Image Thumbnails */}
          <div className="px-4 py-3 bg-white">
            <div className="flex gap-2 overflow-x-auto">
              <div
                onClick={() => setCurrentImage(targetProduct.image)}
                className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                  currentImage === targetProduct.image
                    ? "border-rose-500 shadow-lg"
                    : "border-gray-200"
                }`}
              >
                <img
                  src={targetProduct.image}
                  alt="Main"
                  className="w-full h-full object-cover"
                />
              </div>
              {targetProduct.subImages.map((img, i) => (
                <div
                  key={i}
                  onClick={() => setCurrentImage(img)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                    currentImage === img
                      ? "border-rose-500 shadow-lg"
                      : "border-gray-200"
                  }`}
                >
                  <img
                    src={img}
                    alt={`View ${i + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className="px-4 py-6 space-y-6">
          {/* Title and Rating */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Badge className="bg-green-100 text-green-700 px-2 py-0.5 text-xs">
                IN STOCK
              </Badge>
              <span className="text-sm text-gray-500">
                SKU: {targetProduct.id.toString().padStart(6, "0")}
              </span>
            </div>

            <h1 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">
              {targetProduct.name}
            </h1>

            <div className="flex items-center gap-3 mb-3">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-yellow-400" />
                ))}
              </div>
              <span className="text-sm text-gray-600">4.8 (23 reviews)</span>
            </div>

            <p className="text-gray-600 text-sm leading-relaxed">
              {targetProduct.description.substring(0, 120)}...
            </p>
          </div>

          {/* Price Section */}
          <div className="bg-gradient-to-r from-rose-50 to-pink-50 rounded-2xl p-4">
            <div className="flex items-baseline gap-3 mb-3">
              <span className="text-3xl font-bold text-gray-900">
                ${targetProduct.price}.00
              </span>
              <span className="text-lg text-gray-500 line-through">
                ${(targetProduct.price * 1.2).toFixed(2)}
              </span>
              <span className="bg-red-100 text-red-700 px-2 py-1 text-xs font-medium rounded-full">
                Save 20%
              </span>
            </div>

            {/* Flash Sale Timer */}
            <div className="bg-white rounded-xl p-3 border border-rose-100">
              <div className="flex items-center gap-2 text-rose-600 mb-2">
                <Clock className="w-4 h-4" />
                <span className="text-sm font-medium">Flash Sale Ends In:</span>
              </div>
              <div className="flex gap-2">
                <div className="bg-rose-500 text-white rounded-lg px-2 py-1 text-center min-w-[50px]">
                  <div className="text-lg font-bold">
                    {formatTime(timeLeft).split(":")[0]}
                  </div>
                  <div className="text-xs">Hours</div>
                </div>
                <div className="bg-rose-500 text-white rounded-lg px-2 py-1 text-center min-w-[50px]">
                  <div className="text-lg font-bold">
                    {formatTime(timeLeft).split(":")[1]}
                  </div>
                  <div className="text-xs">Mins</div>
                </div>
                <div className="bg-rose-500 text-white rounded-lg px-2 py-1 text-center min-w-[50px]">
                  <div className="text-lg font-bold">
                    {formatTime(timeLeft).split(":")[2]}
                  </div>
                  <div className="text-xs">Secs</div>
                </div>
              </div>
            </div>
          </div>

          {/* Size & Color Selection */}
          {(targetProduct.sizes?.length > 0 ||
            targetProduct.colors?.length > 0) && (
            <div className="space-y-4">
              {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">
                    Size
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {targetProduct.sizes.map((size) => (
                      <button
                        key={size}
                        className={`px-4 py-2 border rounded-xl text-sm font-medium transition-all ${
                          selectedSize === size
                            ? "border-rose-500 bg-rose-500 text-white"
                            : "border-gray-300 text-gray-700 hover:border-rose-300"
                        }`}
                        onClick={() => setSelectedSize(size)}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {targetProduct.colors && targetProduct.colors.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">
                    Color
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {targetProduct.colors.map((color) => (
                      <button
                        key={color}
                        className={`px-4 py-2 border rounded-xl text-sm font-medium transition-all ${
                          selectedColor === color
                            ? "border-rose-500 bg-rose-500 text-white"
                            : "border-gray-300 text-gray-700 hover:border-rose-300"
                        }`}
                        onClick={() => setSelectedColor(color)}
                      >
                        {color}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Quantity Selector */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-2">Quantity</h3>
            <div className="flex items-center bg-gray-50 rounded-xl p-1 w-fit">
              <button
                className="w-10 h-10 flex items-center justify-center hover:bg-white rounded-lg transition-colors"
                onClick={() => cartCount > 1 && setCartCount(cartCount - 1)}
              >
                <Minus className="w-4 h-4" />
              </button>
              <span className="w-12 text-center font-medium">{cartCount}</span>
              <button
                className="w-10 h-10 flex items-center justify-center hover:bg-white rounded-lg transition-colors"
                onClick={() => setCartCount(cartCount + 1)}
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <AddToCartButton
              product={targetProduct}
              quantity={cartCount}
              selectedSize={selectedSize}
              selectedColor={selectedColor}
            />
            <Button
              className="w-full py-3 bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600 text-white rounded-xl font-medium"
              onClick={() => {
                const addToCartBtn =
                  document.querySelector("[data-add-to-cart]");
                if (addToCartBtn) {
                  addToCartBtn.click();
                  setTimeout(() => {
                    window.location.href = "/checkout";
                  }, 500);
                }
              }}
            >
              <Zap className="w-5 h-5 mr-2" />
              Buy Now
            </Button>
          </div>

          {/* Features */}
          <div className="bg-gray-50 rounded-2xl p-4">
            <h3 className="font-medium text-gray-900 mb-3">
              Why Choose This Product?
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Truck className="w-4 h-4 text-green-600" />
                <span>Free shipping on orders over $100</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Shield className="w-4 h-4 text-blue-600" />
                <span>Secure payment & data protection</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <RotateCcw className="w-4 h-4 text-purple-600" />
                <span>30-day money-back guarantee</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Award className="w-4 h-4 text-amber-600" />
                <span>Premium quality & authentic products</span>
              </div>
            </div>
          </div>

          {/* Product Tabs */}
          <div className="bg-white rounded-2xl border border-gray-100 overflow-hidden">
            <Tabs className="w-full">
              <TabList
                aria-label="Product Information"
                className="flex border-b border-gray-100 bg-gray-50"
              >
                <Tab
                  id="description"
                  className="flex-1 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 cursor-pointer border-b-2 border-transparent hover:border-rose-300 focus:outline-none"
                >
                  Details
                </Tab>
                <Tab
                  id="reviews"
                  className="flex-1 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 cursor-pointer border-b-2 border-transparent hover:border-rose-300 focus:outline-none"
                >
                  Reviews
                </Tab>
              </TabList>

              <TabPanel id="description" className="p-4">
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">
                    Product Description
                  </h4>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {targetProduct.description}
                  </p>
                  <div className="pt-2">
                    <h5 className="font-medium text-gray-900 mb-2">
                      Key Benefits:
                    </h5>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li className="flex items-center gap-2">
                        <Check className="w-3 h-3 text-green-600" />
                        <span>Dermatologically tested</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Check className="w-3 h-3 text-green-600" />
                        <span>Natural ingredients</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Check className="w-3 h-3 text-green-600" />
                        <span>Cruelty-free formula</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </TabPanel>

              <TabPanel id="reviews" className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400" />
                      ))}
                    </div>
                    <span className="text-sm font-medium">4.8 out of 5</span>
                    <span className="text-sm text-gray-500">(23 reviews)</span>
                  </div>

                  <div className="space-y-3">
                    <div className="bg-gray-50 rounded-xl p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-6 h-6 bg-rose-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          S
                        </div>
                        <span className="text-sm font-medium">Sarah M.</span>
                        <div className="flex text-yellow-400">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="w-3 h-3 fill-yellow-400" />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600">
                        "Amazing product! Works exactly as described and arrived
                        quickly."
                      </p>
                    </div>

                    <div className="bg-gray-50 rounded-xl p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          M
                        </div>
                        <span className="text-sm font-medium">Mike R.</span>
                        <div className="flex text-yellow-400">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="w-3 h-3 fill-yellow-400" />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600">
                        "Great value for money. Highly recommend!"
                      </p>
                    </div>
                  </div>
                </div>
              </TabPanel>
            </Tabs>
          </div>

          {/* Similar Products */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">
              You might also like
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {similarProducts.slice(0, 4).map((product) => (
                <Link
                  key={product.id}
                  href={`/products/${product.slug}?hero=9`}
                  className="group"
                >
                  <div className="bg-gray-50 rounded-xl p-3 hover:bg-gray-100 transition-colors">
                    <div className="aspect-square bg-white rounded-lg mb-2 overflow-hidden">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-full object-contain p-2 group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                      {product.name}
                    </h4>
                    <div className="text-sm font-bold text-rose-600">
                      ${product.price}.00
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 10 - Modern Split Screen Design
function ProductVariant10({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-6 py-4 max-w-7xl">
          <div className="flex items-center text-sm text-gray-500">
            <Link href="/" className="hover:text-gray-900 transition-colors">
              Home
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <Link
              href="/products"
              className="hover:text-gray-900 transition-colors"
            >
              Products
            </Link>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-gray-900 font-medium">
              {targetProduct.name}
            </span>
          </div>
        </div>
      </div>

      {/* Main Split Screen Layout */}
      <div className="min-h-screen flex">
        {/* Left Side - Product Images */}
        <div className="w-1/2 bg-white flex flex-col">
          {/* Main Image */}
          <div className="flex-1 flex items-center justify-center p-12">
            <div className="relative max-w-lg w-full">
              <div className="aspect-square bg-gray-50 rounded-3xl overflow-hidden group">
                <img
                  src={currentImage}
                  alt={targetProduct.name}
                  className="w-full h-full object-contain p-8 transition-transform duration-500 group-hover:scale-105"
                />

                {/* Floating Badges */}
                <div className="absolute top-6 left-6 flex flex-col gap-2">
                  <Badge className="bg-black text-white px-3 py-1 text-sm font-medium rounded-full">
                    PREMIUM
                  </Badge>
                  <Badge className="bg-red-500 text-white px-3 py-1 text-sm font-medium rounded-full">
                    -20% OFF
                  </Badge>
                </div>

                {/* Action Buttons */}
                <div className="absolute top-6 right-6 flex flex-col gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/80 backdrop-blur-sm hover:bg-white rounded-full shadow-lg"
                    onClick={async () => {
                      try {
                        const result = await addToWishlist({
                          productId: targetProduct.id,
                          name: targetProduct.name,
                          price: targetProduct.price,
                          image: targetProduct.image,
                        });
                        if (result.success) {
                          toast({
                            title: "Added to wishlist",
                            description: `${targetProduct.name} has been added to your wishlist`,
                          });
                        }
                      } catch (error) {
                        toast({
                          title: "Error",
                          description: "Failed to add product to wishlist",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white/80 backdrop-blur-sm hover:bg-white rounded-full shadow-lg"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Thumbnail Gallery */}
          <div className="px-12 pb-8">
            <div className="flex gap-4 justify-center">
              <div
                onClick={() => setCurrentImage(targetProduct.image)}
                className={`w-20 h-20 rounded-xl overflow-hidden cursor-pointer border-2 transition-all ${
                  currentImage === targetProduct.image
                    ? "border-black shadow-lg"
                    : "border-gray-200 hover:border-gray-400"
                }`}
              >
                <img
                  src={targetProduct.image}
                  alt="Main"
                  className="w-full h-full object-cover"
                />
              </div>
              {targetProduct.subImages.map((img, i) => (
                <div
                  key={i}
                  onClick={() => setCurrentImage(img)}
                  className={`w-20 h-20 rounded-xl overflow-hidden cursor-pointer border-2 transition-all ${
                    currentImage === img
                      ? "border-black shadow-lg"
                      : "border-gray-200 hover:border-gray-400"
                  }`}
                >
                  <img
                    src={img}
                    alt={`View ${i + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Side - Product Details */}
        <div className="w-1/2 bg-gradient-to-br from-gray-900 to-black text-white flex flex-col">
          <div className="flex-1 p-12 overflow-y-auto">
            <div className="max-w-lg space-y-8">
              {/* Product Header */}
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <Badge className="bg-green-500/20 text-green-400 px-3 py-1 text-xs rounded-full border border-green-500/30">
                    IN STOCK
                  </Badge>
                  <span className="text-sm text-gray-400">
                    SKU: {targetProduct.id.toString().padStart(6, "0")}
                  </span>
                  <div className="flex items-center text-gray-400 text-sm">
                    <Eye className="w-4 h-4 mr-1" />
                    <span>{viewersCount} viewing</span>
                  </div>
                </div>

                <h1 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  {targetProduct.name}
                </h1>

                <div className="flex items-center gap-4 mb-6">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400" />
                    ))}
                  </div>
                  <span className="text-gray-300">4.8 (23 Reviews)</span>
                </div>

                <p className="text-gray-300 leading-relaxed text-lg">
                  {targetProduct.description.substring(0, 200)}...
                </p>
              </div>

              {/* Price Section */}
              <div className="bg-white/5 rounded-2xl p-6 backdrop-blur-sm border border-white/10">
                <div className="flex items-baseline gap-4 mb-4">
                  <span className="text-4xl font-bold text-white">
                    ${targetProduct.price}.00
                  </span>
                  <span className="text-xl text-gray-400 line-through">
                    ${(targetProduct.price * 1.2).toFixed(2)}
                  </span>
                  <span className="bg-red-500/20 text-red-400 px-3 py-1 text-sm font-medium rounded-full border border-red-500/30">
                    SAVE 20%
                  </span>
                </div>

                {/* Flash Sale Timer */}
                <div className="bg-gradient-to-r from-amber-500/20 to-orange-500/20 rounded-xl p-4 border border-amber-500/30">
                  <div className="flex items-center gap-2 text-amber-400 mb-3">
                    <Clock className="w-5 h-5" />
                    <span className="font-medium">Flash Sale Ends In:</span>
                  </div>
                  <div className="flex gap-3">
                    <div className="bg-amber-500/30 rounded-lg px-3 py-2 text-center min-w-[60px] border border-amber-500/50">
                      <div className="text-xl font-bold text-white">
                        {formatTime(timeLeft).split(":")[0]}
                      </div>
                      <div className="text-xs text-amber-300">Hours</div>
                    </div>
                    <div className="bg-amber-500/30 rounded-lg px-3 py-2 text-center min-w-[60px] border border-amber-500/50">
                      <div className="text-xl font-bold text-white">
                        {formatTime(timeLeft).split(":")[1]}
                      </div>
                      <div className="text-xs text-amber-300">Mins</div>
                    </div>
                    <div className="bg-amber-500/30 rounded-lg px-3 py-2 text-center min-w-[60px] border border-amber-500/50">
                      <div className="text-xl font-bold text-white">
                        {formatTime(timeLeft).split(":")[2]}
                      </div>
                      <div className="text-xs text-amber-300">Secs</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Options Section */}
              <div className="space-y-6">
                {/* Size Selector */}
                {targetProduct.sizes && targetProduct.sizes.length > 0 && (
                  <div>
                    <h3 className="text-white font-medium mb-3">Size</h3>
                    <div className="flex flex-wrap gap-3">
                      {targetProduct.sizes.map((size) => (
                        <button
                          key={size}
                          className={`px-4 py-2 rounded-xl border transition-all ${
                            selectedSize === size
                              ? "border-white bg-white text-black"
                              : "border-white/30 text-white hover:border-white/60 hover:bg-white/10"
                          }`}
                          onClick={() => setSelectedSize(size)}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selector */}
                {targetProduct.colors && targetProduct.colors.length > 0 && (
                  <div>
                    <h3 className="text-white font-medium mb-3">Color</h3>
                    <div className="flex flex-wrap gap-3">
                      {targetProduct.colors.map((color) => (
                        <button
                          key={color}
                          className={`px-4 py-2 rounded-xl border transition-all ${
                            selectedColor === color
                              ? "border-white bg-white text-black"
                              : "border-white/30 text-white hover:border-white/60 hover:bg-white/10"
                          }`}
                          onClick={() => setSelectedColor(color)}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity Selector */}
                <div>
                  <h3 className="text-white font-medium mb-3">Quantity</h3>
                  <div className="flex items-center bg-white/10 rounded-xl p-1 w-fit border border-white/20">
                    <button
                      className="w-12 h-12 flex items-center justify-center hover:bg-white/20 rounded-lg transition-colors text-white"
                      onClick={() =>
                        cartCount > 1 && setCartCount(cartCount - 1)
                      }
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="w-16 text-center font-medium text-white">
                      {cartCount}
                    </span>
                    <button
                      className="w-12 h-12 flex items-center justify-center hover:bg-white/20 rounded-lg transition-colors text-white"
                      onClick={() => setCartCount(cartCount + 1)}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <AddToCartButton
                  product={targetProduct}
                  quantity={cartCount}
                  selectedSize={selectedSize}
                  selectedColor={selectedColor}
                />
                <Button
                  className="w-full py-4 bg-white text-black hover:bg-gray-100 rounded-xl font-medium text-lg"
                  onClick={() => {
                    const addToCartBtn =
                      document.querySelector("[data-add-to-cart]");
                    if (addToCartBtn) {
                      addToCartBtn.click();
                      setTimeout(() => {
                        window.location.href = "/checkout";
                      }, 500);
                    }
                  }}
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Buy Now
                </Button>
              </div>

              {/* Features */}
              <div className="bg-white/5 rounded-2xl p-6 backdrop-blur-sm border border-white/10">
                <h3 className="text-white font-bold text-lg mb-4">
                  Why Choose This Product?
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                      <Truck className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        Free Shipping
                      </div>
                      <div className="text-gray-400 text-sm">
                        On orders over $100
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center border border-blue-500/30">
                      <Shield className="w-5 h-5 text-blue-400" />
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        Secure Payment
                      </div>
                      <div className="text-gray-400 text-sm">SSL encrypted</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center border border-purple-500/30">
                      <RotateCcw className="w-5 h-5 text-purple-400" />
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        30-Day Returns
                      </div>
                      <div className="text-gray-400 text-sm">
                        Money back guarantee
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-pink-500/20 flex items-center justify-center border border-pink-500/30">
                      <Award className="w-5 h-5 text-pink-400" />
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        Premium Quality
                      </div>
                      <div className="text-gray-400 text-sm">
                        Tested & certified
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Similar Products */}
              <div>
                <h3 className="text-white font-bold text-lg mb-4">
                  You Might Also Like
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {similarProducts.slice(0, 4).map((product) => (
                    <Link
                      key={product.id}
                      href={`/products/${product.slug}?hero=10`}
                      className="group"
                    >
                      <div className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300">
                        <div className="aspect-square bg-white/10 rounded-lg mb-3 overflow-hidden">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-contain p-2 group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <h4 className="font-medium text-white mb-1 line-clamp-2 text-sm">
                          {product.name}
                        </h4>
                        <div className="text-lg font-bold text-white">
                          ${product.price}.00
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 11 - Sidebar Layout
function ProductVariant11({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-white flex">
      <div className="w-80 bg-gray-50 p-6 space-y-6">
        <h1 className="text-2xl font-bold">{targetProduct.name}</h1>
        <div className="text-3xl font-bold text-green-600">
          ${targetProduct.price}.00
        </div>
        <AddToCartButton
          product={targetProduct}
          quantity={cartCount}
          selectedSize={selectedSize}
          selectedColor={selectedColor}
        />
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Truck className="w-4 h-4" /> Free Shipping
          </div>
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4" /> Secure Payment
          </div>
        </div>
      </div>
      <div className="flex-1 p-12 flex items-center justify-center">
        <img
          src={currentImage}
          alt={targetProduct.name}
          className="max-w-lg w-full h-auto object-contain"
        />
      </div>
    </div>
  );
}

// Product Variant 12 - Comparison Layout
function ProductVariant12({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50">
      <div className="container mx-auto px-6 py-12 max-w-7xl">
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 bg-white rounded-3xl p-8 shadow-xl">
            <img
              src={currentImage}
              alt={targetProduct.name}
              className="w-full h-96 object-contain mb-6"
            />
            <h1 className="text-4xl font-bold mb-4">{targetProduct.name}</h1>
            <div className="text-3xl font-bold text-indigo-600 mb-6">
              ${targetProduct.price}.00
            </div>
            <AddToCartButton
              product={targetProduct}
              quantity={cartCount}
              selectedSize={selectedSize}
              selectedColor={selectedColor}
            />
          </div>
          <div className="space-y-6">
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="font-bold mb-4">Product Details</h3>
              <p className="text-gray-600">
                {targetProduct.description.substring(0, 100)}...
              </p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="font-bold mb-4">Reviews</h3>
              <div className="flex items-center gap-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400" />
                  ))}
                </div>
                <span>4.8 (23 reviews)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 13 - Timeline Layout
function ProductVariant13({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-6 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4">{targetProduct.name}</h1>
          <div className="text-4xl font-bold text-yellow-400">
            ${targetProduct.price}.00
          </div>
        </div>
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="bg-gray-800 rounded-3xl p-8">
            <img
              src={currentImage}
              alt={targetProduct.name}
              className="w-full h-96 object-contain"
            />
          </div>
          <div className="space-y-8">
            <div className="border-l-4 border-yellow-400 pl-6">
              <h3 className="text-xl font-bold mb-2">Premium Quality</h3>
              <p className="text-gray-300">Crafted with the finest materials</p>
            </div>
            <div className="border-l-4 border-yellow-400 pl-6">
              <h3 className="text-xl font-bold mb-2">Fast Delivery</h3>
              <p className="text-gray-300">Free shipping worldwide</p>
            </div>
            <AddToCartButton
              product={targetProduct}
              quantity={cartCount}
              selectedSize={selectedSize}
              selectedColor={selectedColor}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 14 - Masonry Layout
function ProductVariant14({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 to-orange-50">
      <div className="container mx-auto px-6 py-12 max-w-7xl">
        <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg break-inside-avoid">
            <img
              src={currentImage}
              alt={targetProduct.name}
              className="w-full h-64 object-contain mb-4"
            />
          </div>
          <div className="bg-white rounded-2xl p-6 shadow-lg break-inside-avoid">
            <h1 className="text-3xl font-bold mb-4">{targetProduct.name}</h1>
            <div className="text-2xl font-bold text-rose-600 mb-4">
              ${targetProduct.price}.00
            </div>
            <AddToCartButton
              product={targetProduct}
              quantity={cartCount}
              selectedSize={selectedSize}
              selectedColor={selectedColor}
            />
          </div>
          <div className="bg-white rounded-2xl p-6 shadow-lg break-inside-avoid">
            <h3 className="font-bold mb-4">Description</h3>
            <p className="text-gray-600">{targetProduct.description}</p>
          </div>
          <div className="bg-white rounded-2xl p-6 shadow-lg break-inside-avoid">
            <h3 className="font-bold mb-4">Features</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-500" /> Premium Quality
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-500" /> Fast Shipping
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-500" /> 30-Day Returns
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Product Variant 15 - Floating Elements Design
function ProductVariant15({
  targetProduct,
  currentImage,
  setCurrentImage,
  cartCount,
  setCartCount,
  viewersCount,
  timeLeft,
  formatTime,
  selectedSize,
  setSelectedSize,
  selectedColor,
  setSelectedColor,
  toast,
  addToWishlist,
  similarProducts,
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse"></div>
      </div>
      <div className="relative z-10 container mx-auto px-6 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold text-white mb-6">
            {targetProduct.name}
          </h1>
          <div className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-pink-400">
            ${targetProduct.price}.00
          </div>
        </div>
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="relative">
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20">
              <img
                src={currentImage}
                alt={targetProduct.name}
                className="w-full h-96 object-contain"
              />
            </div>
          </div>
          <div className="space-y-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <p className="text-white/90 text-lg">
                {targetProduct.description.substring(0, 150)}...
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <AddToCartButton
                product={targetProduct}
                quantity={cartCount}
                selectedSize={selectedSize}
                selectedColor={selectedColor}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ReviewItemProps {
  name: string;
  date: string;
  rating: number;
  verified: boolean;
  content: string;
  image: string;
}

function ReviewItem({
  name,
  date,
  rating,
  verified,
  content,
  image,
}: ReviewItemProps) {
  return (
    <div className="border-b border-gray-200 pb-6">
      <div className="flex items-start">
        <div className="w-10 h-10 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden mr-4">
          <img src={image} alt={name} className="w-full h-full object-cover" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium text-gray-900">{name}</span>
              {verified && (
                <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">
                  Verified Purchase
                </span>
              )}
            </div>
            <span className="text-gray-500 text-sm">{date}</span>
          </div>
          <div className="flex gap-1 items-center mt-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < rating
                    ? "fill-yellow-400 text-yellow-400"
                    : "fill-gray-200 text-gray-200"
                }`}
              />
            ))}
          </div>
          <div className="mt-2">
            <p className="text-gray-700 text-sm">{content}</p>
          </div>
          <div className="mt-3 flex items-center gap-4">
            <button className="text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                />
              </svg>
              Helpful (3)
            </button>
            <button className="text-sm text-gray-500 hover:text-gray-700">
              Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
