"use client";
import Link from "next/link";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Dribbble, Instagram, Menu } from "lucide-react";
import { LINKS } from "@/constants/sideMenuItems";

export function SideBar() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleMenuClick = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <>
      <div className="absolute top-2 size-9  right-5 md:hidden rounded-full bg-slate-900 text-white flex items-center justify-center z-50">
        <Menu className="h-6 w-6 text-white cursor-pointer" onClick={handleMenuClick} />
      </div>

      {/* Backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={handleMenuClick}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-screen w-[285px] bg-slate-900 px-12 py-14 transform z-40 ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } transition-transform duration-300 ease-in-out md:relative md:translate-x-0 md:flex md:flex-col md:sticky md:w-[285px]`}
      >
        <h2 className="text-white text-center text-3xl font-bold self-stretch mt-4">
          Mohammed
        </h2>
        <ul className="w-full mt-4">
          {LINKS.map((link, index) => (
            <motion.li
              key={link.name}
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 },
              }}
              transition={{ duration: 0.5, delay: index * 0.1, type: "spring" }}
              initial="hidden"
              animate="visible"
              className="text-white text-center text-xl leading-8 uppercase self-stretch mt-5"
            >
              <Link href={link.path}>{link.name}</Link>
            </motion.li>
          ))}
        </ul>

        <div className="mt-auto">
          <div className="flex gap-4 items-center mb-4 justify-center">
            <a
              href="#"
              className="h-10 w-10 rounded-full bg-white flex items-center justify-center hover:-translate-y-1 transition-transform"
            >
              <Dribbble />
            </a>
            <a
              href="#"
              className="h-10 w-10 rounded-full bg-white flex items-center justify-center hover:-translate-y-1 transition-transform"
            >
              <Apple />
            </a>
            <a
              href="#"
              className="h-10 w-10 rounded-full bg-white flex items-center justify-center hover:-translate-y-1 transition-transform"
            >
              <Instagram />
            </a>
          </div>
          <p className="text-white text-sm text-center">
            Copyright ©{new Date().getFullYear()}{" "}
            <span className="font-bold hover:underline">Mohamed Dadda</span>. All
            right reserved.
          </p>
        </div>
      </div>
    </>
  );
}
