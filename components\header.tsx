"use client";
import Logo from "@/components/Logo";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useHeadroom } from "@mantine/hooks";
import {
  Menu,
  Search,
  ShoppingBasket,
  User,
  X,
  Trash2,
  Plus,
  Minus,
  Package,
} from "lucide-react";
import Link from "next/link";
import { Badge } from "./ui/badge";
import NavMenuStore from "./NavMenu";
import { motion } from "framer-motion";
import { Input } from "./ui/input";
import { useCart } from "@/lib/CartContext";

export default function Header() {
  const pinned = useHeadroom({ fixedAt: 100 });
  const {
    items: cartItems,
    total: cartTotal,
    removeItem,
    updateQuantity,
    itemCount,
    subtotal,
    shippingCost,
    taxAmount,
  } = useCart();

  return (
    <header
      className={`h-[72px] w-full sticky top-0 z-30 transition-all duration-300 ${
        !pinned
          ? "shadow-lg backdrop-blur-0 bg-white"
          : "backdrop-blur-xl bg-white/75 border-b border-gray-100"
      }`}
    >
      <nav className="container mx-auto flex items-center justify-between h-full px-4 sm:px-6">
        <div className="flex items-center gap-6">
          <MobileNav />
          <Link href="/" className="transition-opacity hover:opacity-80">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Logo />
            </motion.div>
          </Link>
        </div>

        <div className="items-center gap-6 hidden md:flex">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex items-center gap-6"
          >
            <NavMenuStore />
          </motion.div>
        </div>

        <div className="flex items-center gap-5">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
            className="flex items-center gap-5"
          >
            {/* Search Button with Sheet */}
            <Sheet>
              <SheetTrigger asChild>
                <button className="relative p-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
                  <Search className="h-[20px] w-[20px] text-gray-700" />
                </button>
              </SheetTrigger>
              <SheetContent side="top" className="w-full py-6">
                <div className="space-y-6 max-w-2xl mx-auto">
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold">Search</h2>
                    <Button variant="ghost" size="icon">
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="relative">
                    <Input
                      placeholder="Search for products..."
                      className="w-full pl-10 py-6 text-lg"
                      autoFocus
                    />
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
                  </div>
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-gray-500">
                      Popular Searches
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {[
                        "Sneakers",
                        "T-shirts",
                        "Summer collection",
                        "Accessories",
                        "New arrivals",
                      ].map((term) => (
                        <div
                          key={term}
                          className="px-3 py-1.5 bg-gray-100 rounded-full text-sm hover:bg-gray-200 transition-colors cursor-pointer"
                        >
                          {term}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>

            {/* User Button with Sheet */}
            <Sheet>
              <SheetTrigger asChild>
                <button className="relative p-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
                  <User className="h-[20px] w-[20px] text-gray-700" />
                </button>
              </SheetTrigger>
              <SheetContent className="w-[320px] sm:w-[400px]">
                <div className="flex flex-col h-full">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">Account</h2>
                    <Button variant="ghost" size="icon">
                      <X className="h-4 w-4" />
                    </Button>
                  </div>{" "}
                  <div className="space-y-6 flex-1">
                    {/* Login Links */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Account</h3>
                      <div className="space-y-3">
                        <Link href="/profile">
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                          >
                            <User className="h-4 w-4 mr-2" />
                            My Profile
                          </Button>
                        </Link>
                        <Link href="/my-orders">
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                          >
                            <Package className="h-4 w-4 mr-2" />
                            My Orders
                          </Button>
                        </Link>
                        <Link href="/auth/sign-in">
                          <Button className="w-full">Sign In</Button>
                        </Link>
                        <Link href="/auth/sign-up">
                          <Button variant="outline" className="w-full">
                            Create Account
                          </Button>
                        </Link>
                      </div>
                    </div>

                    <div className="relative flex items-center gap-4 py-4">
                      <div className="border-t flex-1" />
                      <span className="text-gray-500 text-sm">OR</span>
                      <div className="border-t flex-1" />
                    </div>

                    <div className="space-y-3">
                      <Button variant="outline" className="w-full">
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                          <path
                            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                            fill="#4285F4"
                          />
                          <path
                            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                            fill="#34A853"
                          />
                          <path
                            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                            fill="#FBBC05"
                          />
                          <path
                            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                            fill="#EA4335"
                          />
                        </svg>
                        Continue with Google
                      </Button>
                      <Button variant="outline" className="w-full">
                        <svg
                          className="w-5 h-5 mr-2 fill-current"
                          viewBox="0 0 24 24"
                        >
                          <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                        </svg>
                        Continue with Facebook
                      </Button>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>

            {/* Cart Button with Sheet */}
            <Sheet>
              <SheetTrigger asChild>
                <button className="relative p-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
                  <ShoppingBasket className="h-[20px] w-[20px] text-gray-700" />
                  {itemCount > 0 && (
                    <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-black text-white text-[10px] font-medium">
                      {itemCount}
                    </Badge>
                  )}
                </button>
              </SheetTrigger>
              <SheetContent className="w-[320px] sm:w-[400px]">
                <div className="flex flex-col h-full">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">Your Cart</h2>
                    <Button variant="ghost" size="icon">
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {cartItems.length === 0 ? (
                    <div className="flex-1 flex flex-col items-center justify-center space-y-4">
                      <ShoppingBasket className="h-16 w-16 text-gray-300" />
                      <p className="text-gray-500">Your cart is empty</p>
                      <Button>Browse Products</Button>
                    </div>
                  ) : (
                    <>
                      <div className="flex-1 overflow-auto space-y-4">
                        {cartItems.map((item) => (
                          <div
                            key={item.id}
                            className="flex gap-4 py-4 border-b"
                          >
                            <div className="w-20 h-20 bg-gray-100 rounded-md overflow-hidden">
                              <img
                                src={item.image || "https://placehold.co/80"}
                                alt={item.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex-1 flex flex-col">
                              <h3 className="font-medium">{item.name}</h3>
                              <p className="text-sm text-gray-500">
                                {item.size && `Size: ${item.size}`}{" "}
                                {item.color && `• ${item.color}`}
                              </p>
                              <div className="mt-auto flex justify-between items-center">
                                <div className="flex items-center border rounded">
                                  <button
                                    className="p-1"
                                    onClick={() =>
                                      updateQuantity(item.id, item.quantity - 1)
                                    }
                                  >
                                    <Minus className="h-3 w-3" />
                                  </button>
                                  <span className="px-2">{item.quantity}</span>
                                  <button
                                    className="p-1"
                                    onClick={() =>
                                      updateQuantity(item.id, item.quantity + 1)
                                    }
                                  >
                                    <Plus className="h-3 w-3" />
                                  </button>
                                </div>
                                <span className="font-medium">
                                  ${item.price.toFixed(2)}
                                </span>
                              </div>
                            </div>
                            <button
                              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                              onClick={() => removeItem(item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>

                      <div className="border-t pt-4 mt-4 space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-500">Subtotal</span>
                          <span className="font-medium">
                            ${cartTotal.toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-500">Shipping</span>
                          <span className="font-medium">
                            Calculated at checkout
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-lg font-semibold">
                          <span>Total</span>
                          <span>${cartTotal.toFixed(2)}</span>
                        </div>
                        <Button asChild className="w-full py-6" size="lg">
                          <Link
                            href="/checkout"
                            className="flex items-center justify-center gap-2"
                          >
                            <ShoppingBasket className="h-4 w-4" />
                            Checkout
                          </Link>
                        </Button>
                        <Button asChild variant="outline" className="w-full">
                          <Link href="/products">Continue Shopping</Link>
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </motion.div>
        </div>
      </nav>
    </header>
  );
}

function MobileNav() {
  const Links = ["Home", "Store", "About", "Contact"];
  const { itemCount, wishlist } = useCart();
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button size={"icon"} variant={"ghost"} className="sm:hidden">
          <Menu className="h-6 w-6" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[320px] sm:w-[380px] p-0" side={"left"}>
        <div className="h-full flex flex-col">
          <div className="p-6 border-b">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Nayzak</h1>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Search products..."
                className="w-full bg-gray-100 rounded-lg py-3 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-black/10 transition-all"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
            </div>
          </div>

          <div className="flex-1 overflow-auto">
            <nav className="p-6">
              <Accordion type="single" collapsible className="w-full">
                {Links.map((link, index) => (
                  <AccordionItem
                    value={link}
                    key={index}
                    className="border-b border-gray-200"
                  >
                    <AccordionTrigger className="py-4 hover:no-underline">
                      <span className="font-medium">{link}</span>
                    </AccordionTrigger>
                    <AccordionContent className="px-2 pb-4">
                      <div className="space-y-3">
                        <a
                          href="#"
                          className="block py-2 px-3 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                          {link} Category 1
                        </a>
                        <a
                          href="#"
                          className="block py-2 px-3 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                          {link} Category 2
                        </a>
                        <a
                          href="#"
                          className="block py-2 px-3 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                          {link} Category 3
                        </a>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </nav>
          </div>

          <div className="border-t p-6 space-y-5">
            <div className="flex flex-col space-y-3">
              <div className="flex justify-between items-center">
                <span className="font-medium">Cart</span>
                <span className="bg-black text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                  {itemCount}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Wishlist</span>
                <span className="bg-black text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                  {wishlist.length}
                </span>
              </div>
            </div>

            <div className="pt-4 space-y-3">
              <Button variant="outline" className="w-full justify-start gap-2">
                <User className="h-4 w-4" />
                Sign in
              </Button>
              <Button className="w-full">Create Account</Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
