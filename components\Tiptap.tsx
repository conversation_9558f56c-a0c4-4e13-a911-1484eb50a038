"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Toggle } from "./ui/toggle";
import { Bold } from "lucide-react";

const Tiptap = () => {
  const editor = useEditor({
    extensions: [StarterKit],
    content: "<p>Hello World! 🌎️</p>",
  });

  return (
    <>
      <div className="flex p-2">
        <Toggle
          pressed={editor?.isActive("bold")}
          onClick={() => editor?.chain().focus().toggleBold().run()}
        >
          <Bold className="h-5 w-5" />
        </Toggle>
      </div>
      <div className="border p-3 rounded max-w-xl self-strech">
        <EditorContent editor={editor} />
      </div>
    </>
  );
};

export default Tiptap;
