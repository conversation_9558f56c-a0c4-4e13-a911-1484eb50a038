"use client";

import { forwardRef, InputHTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { cva, type VariantProps } from "class-variance-authority";
import { Check, AlertCircle } from "lucide-react";

const inputVariants = cva(
  "flex h-11 w-full rounded-xl border border-input bg-background px-4 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 shadow-sm",
  {
    variants: {
      variant: {
        default:
          "border-gray-200 focus-visible:border-primary hover:border-gray-300 focus-visible:shadow-md",
        error:
          "border-red-300 text-red-900 focus-visible:border-red-500 focus-visible:ring-red-500/20 bg-red-50 hover:border-red-400",
        success:
          "border-green-300 text-green-900 focus-visible:border-green-500 focus-visible:ring-green-500/20 bg-green-50 hover:border-green-400",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

interface AuthInputProps
  extends InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  error?: string;
  success?: boolean;
}

const AuthInput = forwardRef<HTMLInputElement, AuthInputProps>(
  ({ className, variant, error, success, ...props }, ref) => {
    // Determine variant based on error or success
    const computedVariant = error ? "error" : success ? "success" : variant;

    return (
      <div className="relative">
        <input
          className={cn(inputVariants({ variant: computedVariant }), className)}
          ref={ref}
          {...props}
        />

        {/* Status icons */}
        <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="text-red-500"
              >
                <AlertCircle className="h-4 w-4" />
              </motion.div>
            )}
            {success && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="text-green-500"
              >
                <Check className="h-4 w-4" />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Error message */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-xs text-red-500 mt-1.5 ml-1"
            >
              {error}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

AuthInput.displayName = "AuthInput";

export { AuthInput };
