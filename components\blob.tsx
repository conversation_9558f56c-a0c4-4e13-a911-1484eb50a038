import React from "react";

export default function Blob() {
  return (
    <div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={216}
        height={136}
        fill="none"
      >
        <path
          fill="url(#a)"
          d="M215.637 0 0 124.498C100.654 160.028 196.291 104.812 215.637 0Z"
        />
        <defs>
          <linearGradient
            id="a"
            x1={224.856}
            x2={37.227}
            y1={44.099}
            y2={150.04}
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FFB147" />
            <stop offset={0.52} stopColor="#FF6C63" />
            <stop offset={1} stopColor="#B86ADF" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
}
