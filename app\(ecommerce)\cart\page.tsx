"use client";

import { useState } from "react";
import { useCart } from "@/lib/CartContext";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ShoppingBag,
  ArrowRight,
  Trash2,
  Heart,
  Clock,
  ChevronDown,
  ChevronUp,
  Check,
  X,
  AlertCircle,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { validateCoupon } from "@/actions/cart-actions";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

export default function Cart() {
  const {
    items,
    removeItem,
    updateQuantity,
    subtotal,
    total,
    shippingCost,
    freeShippingThreshold,
    remainingForFreeShipping,
    taxRate,
    taxAmount,
    moveToWishlist,
    moveToSavedForLater,
    savedForLater,
    moveToCart,
    removeFromSavedForLater,
    coupons,
    applyCoupon,
    removeCoupon,
  } = useCart();

  const [couponCode, setCouponCode] = useState("");
  const [couponError, setCouponError] = useState("");
  const [couponSuccess, setCouponSuccess] = useState("");
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);
  const [showSavedItems, setShowSavedItems] = useState(true);
  const { toast } = useToast();

  // Handle applying coupon
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponError("Please enter a coupon code");
      return;
    }

    setIsApplyingCoupon(true);
    setCouponError("");
    setCouponSuccess("");

    try {
      // First validate the coupon with the server
      const result = await validateCoupon(couponCode, subtotal);

      if (result.success) {
        // Then apply it to the client-side cart
        const applied = applyCoupon(couponCode);

        if (applied) {
          setCouponSuccess(result.message);
          setCouponCode("");
          toast({
            title: "Coupon applied",
            description: result.message,
          });
        } else {
          setCouponError("This coupon has already been applied");
        }
      } else {
        setCouponError(result.message);
      }
    } catch (error) {
      setCouponError("Failed to apply coupon");
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 font-sans">
      <h1 className="text-3xl font-bold text-center mb-6">
        Your Shopping Cart
      </h1>

      {items.length === 0 && savedForLater.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <ShoppingBag className="h-12 w-12 text-gray-400" />
          </div>
          <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
          <p className="text-gray-500 mb-8">
            Looks like you haven't added any products to your cart yet.
          </p>
          <Button asChild>
            <Link href="/products">Browse Products</Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            {items.length === 0 ? (
              <div className="bg-gray-50 p-6 rounded-lg mb-6">
                <p className="text-gray-500 text-center">
                  Your cart is empty, but you have items saved for later.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">
                  Cart Items ({items.length})
                </h2>

                {items.map((item) => (
                  <div
                    key={item.id}
                    className="flex gap-4 border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                  >
                    <div className="w-24 h-24 bg-gray-100 rounded-md flex-shrink-0 relative overflow-hidden">
                      {item.image && (
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover rounded-md"
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <h3 className="font-medium">{item.name}</h3>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-gray-400 hover:text-red-500 transition-colors"
                          aria-label="Remove item"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="text-sm text-gray-500 mb-2">
                        {item.size && <span>Size: {item.size}</span>}
                        {item.color && (
                          <span className="ml-2">Color: {item.color}</span>
                        )}
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center border rounded">
                          <button
                            className="px-2 py-1"
                            onClick={() =>
                              updateQuantity(item.id, item.quantity - 1)
                            }
                            aria-label="Decrease quantity"
                          >
                            -
                          </button>
                          <span className="px-2">{item.quantity}</span>
                          <button
                            className="px-2 py-1"
                            onClick={() =>
                              updateQuantity(item.id, item.quantity + 1)
                            }
                            aria-label="Increase quantity"
                          >
                            +
                          </button>
                        </div>
                        <div className="font-medium">
                          ${(item.price * item.quantity).toFixed(2)}
                        </div>
                      </div>

                      {/* Item Actions */}
                      <div className="flex gap-4 mt-3 text-sm">
                        <button
                          className="text-gray-500 hover:text-gray-700 flex items-center gap-1"
                          onClick={() => {
                            moveToSavedForLater(item.id);
                            toast({
                              title: "Saved for later",
                              description: `${item.name} has been saved for later`,
                            });
                          }}
                        >
                          <Clock className="h-3 w-3" />
                          Save for later
                        </button>

                        <button
                          className="text-gray-500 hover:text-gray-700 flex items-center gap-1"
                          onClick={() => {
                            moveToWishlist(item.id);
                            toast({
                              title: "Added to wishlist",
                              description: `${item.name} has been added to your wishlist`,
                            });
                          }}
                        >
                          <Heart className="h-3 w-3" />
                          Move to wishlist
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Saved For Later Items */}
            {savedForLater.length > 0 && (
              <div className="mt-8 border-t pt-6">
                <div
                  className="flex justify-between items-center cursor-pointer mb-4"
                  onClick={() => setShowSavedItems(!showSavedItems)}
                >
                  <h2 className="text-xl font-semibold">
                    Saved for Later ({savedForLater.length})
                  </h2>
                  <button className="text-gray-500">
                    {showSavedItems ? (
                      <ChevronUp className="h-5 w-5" />
                    ) : (
                      <ChevronDown className="h-5 w-5" />
                    )}
                  </button>
                </div>

                <AnimatePresence>
                  {showSavedItems && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4 overflow-hidden"
                    >
                      {savedForLater.map((item) => (
                        <div
                          key={item.id}
                          className="flex gap-4 border border-gray-200 rounded-lg p-4 bg-gray-50"
                        >
                          <div className="w-20 h-20 bg-gray-100 rounded-md flex-shrink-0 relative overflow-hidden">
                            {item.image && (
                              <Image
                                src={item.image}
                                alt={item.name}
                                fill
                                className="object-cover rounded-md"
                              />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between">
                              <h3 className="font-medium">{item.name}</h3>
                              <button
                                onClick={() => removeFromSavedForLater(item.id)}
                                className="text-gray-400 hover:text-red-500 transition-colors"
                                aria-label="Remove saved item"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                            <div className="text-sm text-gray-500 mb-2">
                              {item.size && <span>Size: {item.size}</span>}
                              {item.color && (
                                <span className="ml-2">
                                  Color: {item.color}
                                </span>
                              )}
                            </div>
                            <div className="flex justify-between items-center">
                              <div className="font-medium">
                                ${item.price.toFixed(2)}
                              </div>
                            </div>

                            {/* Item Actions */}
                            <div className="flex gap-4 mt-3 text-sm">
                              <button
                                className="text-gray-500 hover:text-gray-700 flex items-center gap-1"
                                onClick={() => {
                                  moveToCart(item.id);
                                  toast({
                                    title: "Moved to cart",
                                    description: `${item.name} has been moved to your cart`,
                                  });
                                }}
                              >
                                <ShoppingBag className="h-3 w-3" />
                                Move to cart
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="border border-gray-200 rounded-lg p-6 sticky top-6">
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

              {/* Free Shipping Progress */}
              {remainingForFreeShipping > 0 && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-700">
                    Add ${remainingForFreeShipping.toFixed(2)} more to get FREE
                    shipping!
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{
                        width: `${Math.min(
                          100,
                          (subtotal / freeShippingThreshold) * 100
                        )}%`,
                      }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Coupon Code */}
              <div className="mb-4">
                <div className="flex gap-2 mb-2">
                  <Input
                    placeholder="Enter coupon code"
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleApplyCoupon}
                    disabled={isApplyingCoupon || !couponCode.trim()}
                    variant="outline"
                  >
                    {isApplyingCoupon ? "Applying..." : "Apply"}
                  </Button>
                </div>

                {couponError && (
                  <div className="text-sm text-red-500 flex items-center gap-1 mt-1">
                    <AlertCircle className="h-3 w-3" />
                    {couponError}
                  </div>
                )}

                {couponSuccess && (
                  <div className="text-sm text-green-500 flex items-center gap-1 mt-1">
                    <Check className="h-3 w-3" />
                    {couponSuccess}
                  </div>
                )}

                {/* Applied Coupons */}
                {coupons.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {coupons.map((coupon) => (
                      <div
                        key={coupon.code}
                        className="flex justify-between items-center bg-gray-50 p-2 rounded text-sm"
                      >
                        <div>
                          <span className="font-medium">{coupon.code}</span>
                          <span className="text-gray-500 ml-2">
                            {coupon.discountType === "percentage"
                              ? `${coupon.discountValue}% off`
                              : `$${coupon.discountValue} off`}
                          </span>
                        </div>
                        <button
                          onClick={() => removeCoupon(coupon.code)}
                          className="text-gray-400 hover:text-red-500"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>

                {coupons.length > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount</span>
                    <span>
                      -$
                      {(
                        subtotal -
                        (subtotal -
                          (coupons[0].discountType === "percentage"
                            ? subtotal * (coupons[0].discountValue / 100)
                            : coupons[0].discountValue))
                      ).toFixed(2)}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>
                    {shippingCost === 0
                      ? "FREE"
                      : `$${shippingCost.toFixed(2)}`}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">
                    Tax ({(taxRate * 100).toFixed(0)}%)
                  </span>
                  <span>${taxAmount.toFixed(2)}</span>
                </div>
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>${total.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <Button
                asChild
                className="w-full py-3 mt-4 font-semibold"
                disabled={items.length === 0}
              >
                <Link
                  href="/checkout"
                  className="flex items-center justify-center gap-2"
                >
                  <ShoppingBag className="h-4 w-4" />
                  Proceed to Checkout
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
