"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { AuthFormLayout } from "@/components/auth/auth-form-layout";
import { AuthInput } from "@/components/auth/auth-input";
import { SocialButtons } from "@/components/auth/social-buttons";
import { useAuth } from "@/lib/auth-context";

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const { signIn, signInWithGoogle, signInWithFacebook } = useAuth();
  const router = useRouter();

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const success = await signIn(email, password);

      if (success) {
        router.push("/"); // Redirect to home page on successful sign in
      } else {
        setError("Invalid email or password. Please try again.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again later.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AuthFormLayout
      imageUrl="https://assets.lummi.ai/assets/QmRSHgT4MqhHnDw4LNwRUwpgcz9SLbWGzRGdh5VYhPkhBp?auto=format&w=1500"
      testimonialText="This platform completely transformed our business workflow. The interface is intuitive and the features are exactly what we needed."
      testimonialAuthor="Sarah Johnson"
      testimonialRole="Product Manager at TechCorp"
    >
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Welcome back</h1>
        <p className="mt-2 text-sm text-muted-foreground">
          Enter your email and password to sign in to your account
        </p>
      </div>
      <form onSubmit={handleSubmit} className="space-y-5">
        {" "}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}{" "}
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <AuthInput
            id="email"
            placeholder="<EMAIL>"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setEmail(e.target.value)
            }
          />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password">Password</Label>
            <Link
              href="/auth/forgot-password"
              className="text-sm font-medium text-primary hover:underline"
            >
              Forgot password?
            </Link>
          </div>
          <AuthInput
            id="password"
            type="password"
            autoComplete="current-password"
            required
            value={password}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setPassword(e.target.value)
            }
          />
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox id="remember" />
          <Label
            htmlFor="remember"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Remember me
          </Label>
        </div>{" "}
        <Button
          type="submit"
          className="w-full h-11 text-base font-medium rounded-xl shadow-sm hover:shadow-md transition-all"
          disabled={isLoading}
        >
          {isLoading ? "Signing in..." : "Sign In"}
        </Button>
      </form>{" "}
      <div className="mt-6 text-center text-sm">
        Don&apos;t have an account?{" "}
        <Link
          href="/auth/sign-up"
          className="font-medium text-primary hover:underline"
        >
          Sign up
        </Link>
      </div>
      <div className="mt-6">
        <SocialButtons
          isLoading={isLoading}
          onGoogleClick={async () => {
            setIsLoading(true);
            setError("");
            try {
              const success = await signInWithGoogle();
              if (success) {
                router.push("/");
              } else {
                setError("Google sign-in failed. Please try again.");
              }
            } catch (err) {
              setError("An error occurred during Google sign-in.");
              console.error(err);
            } finally {
              setIsLoading(false);
            }
          }}
          onFacebookClick={async () => {
            setIsLoading(true);
            setError("");
            try {
              const success = await signInWithFacebook();
              if (success) {
                router.push("/");
              } else {
                setError("Facebook sign-in failed. Please try again.");
              }
            } catch (err) {
              setError("An error occurred during Facebook sign-in.");
              console.error(err);
            } finally {
              setIsLoading(false);
            }
          }}
        />
      </div>
    </AuthFormLayout>
  );
}
