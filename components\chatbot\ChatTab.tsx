"use client";

import React, { useState, useRef, useEffect } from "react";
import {
  Send,
  Bot,
  Sparkles,
  Mic,
  Paperclip,
  Image as ImageIcon,
  File,
  X,
  MoreHorizontal,
  Copy,
  Trash,
  Edit,
  ThumbsUp,
  ThumbsDown,
  Heart,
  Laugh,
  Search,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { type Message, type Attachment } from "./ChatbotWidget";
import { motion } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface ChatTabProps {
  messages: Message[];
  onSendMessage: (content: string, attachments?: Attachment[]) => void;
  isTyping: boolean;
  voiceInputEnabled?: boolean;
}

// Reaction options
const reactionOptions = [
  { emoji: "👍", icon: ThumbsUp, label: "Thumbs Up" },
  { emoji: "👎", icon: ThumbsDown, label: "Thumbs Down" },
  { emoji: "❤️", icon: Heart, label: "Heart" },
  { emoji: "😂", icon: Laugh, label: "Laugh" },
];

const ChatTab: React.FC<ChatTabProps> = ({
  messages,
  onSendMessage,
  isTyping,
  voiceInputEnabled = true,
}) => {
  const [inputValue, setInputValue] = useState("");
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingProgress, setRecordingProgress] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState("");

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() || attachments.length > 0) {
      onSendMessage(inputValue, attachments);
      setInputValue("");
      setAttachments([]);
    }
  };

  // Format time for messages
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Handle file attachment
  const handleFileAttachment = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Process file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // In a real implementation, you would upload these files to a server
    // and get back URLs. For this demo, we'll create fake URLs
    const newAttachments: Attachment[] = Array.from(files).map((file) => ({
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
      type: file.type.startsWith("image/")
        ? "image"
        : file.type.startsWith("audio/")
        ? "audio"
        : "file",
      url: URL.createObjectURL(file), // This creates a temporary local URL
      name: file.name,
      size: file.size,
    }));

    setAttachments([...attachments, ...newAttachments]);

    // Reset the input value so the same file can be selected again
    if (event.target) {
      event.target.value = "";
    }
  };

  // Remove an attachment
  const removeAttachment = (id: string) => {
    setAttachments(attachments.filter((attachment) => attachment.id !== id));
  };

  // Toggle voice recording
  const toggleVoiceRecording = () => {
    setIsRecording(!isRecording);

    if (!isRecording) {
      // Start recording logic would go here
      // For demo purposes, we'll just set a timeout to simulate recording
      setRecordingProgress(0);
      const interval = setInterval(() => {
        setRecordingProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsRecording(false);
            // Simulate transcribed text
            const transcribedText =
              "This is a simulated voice message transcription.";
            onSendMessage(transcribedText);
            return 0;
          }
          return prev + 2;
        });
      }, 100);
    }
  };

  // Toggle search mode
  const toggleSearch = () => {
    setIsSearching(!isSearching);
    if (!isSearching && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    } else {
      setSearchQuery("");
    }
  };

  // Filter messages based on search query
  const filteredMessages = searchQuery.trim()
    ? messages.filter((msg) =>
        msg.content.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : messages;

  // Start editing a message
  const startEditMessage = (message: Message) => {
    if (message.sender === "user") {
      setEditingMessageId(message.id);
      setEditValue(message.content);
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingMessageId(null);
    setEditValue("");
  };

  // Save edited message
  const saveEdit = (messageId: string) => {
    // In a real app, you would update the message in your state/database
    // For this demo, we'll just log it
    console.log(`Message ${messageId} edited to: ${editValue}`);
    cancelEdit();
  };

  // Add reaction to message
  const addReaction = (messageId: string, emoji: string) => {
    // In a real app, you would update the message in your state/database
    // For this demo, we'll just log it
    console.log(`Added reaction ${emoji} to message ${messageId}`);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search Bar (Conditional) */}
      {isSearching && (
        <div className="border-b p-2 flex items-center gap-2 bg-muted/30">
          <Search className="h-4 w-4 text-muted-foreground" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search in conversation..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1 bg-transparent border-none outline-none text-sm"
          />
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 rounded-full"
            onClick={toggleSearch}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="bg-gradient-to-r from-indigo-500/20 to-purple-500/20 p-6 rounded-full mb-6">
              <Sparkles className="h-8 w-8 text-indigo-500" />
            </div>
            <h3 className="font-medium text-xl">How can we help?</h3>
            <p className="text-muted-foreground text-sm mt-3 max-w-[280px]">
              Ask us anything about our products, shipping, or returns.
            </p>
            <div className="mt-8 space-y-2 w-full max-w-[280px]">
              <Button
                onClick={() => onSendMessage("Tell me about your products")}
                variant="outline"
                className="w-full justify-start"
              >
                <Send className="mr-2 h-3 w-3 text-indigo-500" />
                Tell me about your products
              </Button>
              <Button
                onClick={() => onSendMessage("What are your shipping options?")}
                variant="outline"
                className="w-full justify-start"
              >
                <Send className="mr-2 h-3 w-3 text-indigo-500" />
                What are your shipping options?
              </Button>
              <Button
                onClick={() => onSendMessage("How do I return an item?")}
                variant="outline"
                className="w-full justify-start"
              >
                <Send className="mr-2 h-3 w-3 text-indigo-500" />
                How do I return an item?
              </Button>
            </div>
          </div>
        ) : (
          <>
            {filteredMessages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "flex items-start gap-3 group",
                  message.sender === "user" ? "justify-end" : "justify-start"
                )}
              >
                {message.sender === "bot" && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarImage
                      src="/bot-avatar.png"
                      alt="Forever Assistant"
                    />
                    <AvatarFallback className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}

                <div
                  className={cn(
                    "flex flex-col max-w-[85%] sm:max-w-[80%] relative",
                    message.sender === "user" ? "items-end" : "items-start"
                  )}
                >
                  {/* Message Actions Dropdown */}
                  <div
                    className={cn(
                      "absolute top-0 opacity-0 group-hover:opacity-100 md:group-hover:opacity-100 transition-opacity",
                      message.sender === "user"
                        ? "right-full mr-1"
                        : "left-full ml-1"
                    )}
                  >
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 rounded-full bg-muted/80"
                        >
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align={message.sender === "user" ? "end" : "start"}
                        className="w-40"
                      >
                        <DropdownMenuItem
                          onClick={() => {
                            navigator.clipboard.writeText(message.content);
                          }}
                        >
                          <Copy className="h-3.5 w-3.5 mr-2" />
                          Copy text
                        </DropdownMenuItem>

                        {message.sender === "user" && (
                          <DropdownMenuItem
                            onClick={() => startEditMessage(message)}
                          >
                            <Edit className="h-3.5 w-3.5 mr-2" />
                            Edit message
                          </DropdownMenuItem>
                        )}

                        <DropdownMenuSeparator />

                        <DropdownMenuItem className="text-destructive focus:text-destructive">
                          <Trash className="h-3.5 w-3.5 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Message Content */}
                  {editingMessageId === message.id ? (
                    <div className="flex flex-col gap-2 w-full">
                      <Input
                        value={editValue}
                        onChange={(e) => setEditValue(e.target.value)}
                        className="min-w-[200px]"
                        autoFocus
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEdit}
                          className="h-7 px-2 text-xs"
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => saveEdit(message.id)}
                          className="h-7 px-2 text-xs bg-gradient-to-r from-indigo-500 to-purple-500"
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className={cn(
                        "rounded-2xl px-4 py-2.5",
                        message.sender === "user"
                          ? "bg-gradient-to-r from-indigo-500 to-purple-500 text-white"
                          : "bg-muted border border-border"
                      )}
                    >
                      {message.content}

                      {/* Attachments (if any) */}
                      {message.attachments &&
                        message.attachments.length > 0 && (
                          <div className="mt-2 space-y-2">
                            {message.attachments.map((attachment) => (
                              <div
                                key={attachment.id}
                                className={cn(
                                  "rounded-lg p-2 flex items-center gap-2 text-sm",
                                  message.sender === "user"
                                    ? "bg-white/10"
                                    : "bg-background border border-border/50"
                                )}
                              >
                                {attachment.type === "image" ? (
                                  <div className="relative">
                                    <img
                                      src={attachment.url}
                                      alt={attachment.name}
                                      className="w-32 h-24 object-cover rounded"
                                    />
                                    <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/50 rounded">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8 text-white"
                                      >
                                        <ImageIcon className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  <>
                                    <div
                                      className={cn(
                                        "p-2 rounded-full",
                                        message.sender === "user"
                                          ? "bg-white/20"
                                          : "bg-primary/10"
                                      )}
                                    >
                                      <File className="h-4 w-4" />
                                    </div>
                                    <div className="flex-1 truncate">
                                      <div className="font-medium truncate">
                                        {attachment.name}
                                      </div>
                                      <div className="text-xs opacity-70">
                                        {attachment.size
                                          ? `${Math.round(
                                              attachment.size / 1024
                                            )} KB`
                                          : ""}
                                      </div>
                                    </div>
                                  </>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                    </div>
                  )}

                  {/* Message Footer */}
                  <div className="flex items-center mt-1 px-2">
                    <span className="text-xs text-muted-foreground">
                      {formatTime(message.timestamp)}
                      {message.isEdited && (
                        <span className="ml-1">(edited)</span>
                      )}
                    </span>

                    {/* Reactions */}
                    {message.reactions && message.reactions.length > 0 && (
                      <div className="flex ml-2 gap-1">
                        {message.reactions.map((reaction, idx) => (
                          <Badge
                            key={idx}
                            variant="outline"
                            className="px-1.5 py-0 h-5 text-xs bg-muted/50 hover:bg-muted cursor-pointer"
                          >
                            {reaction.emoji}{" "}
                            {reaction.count > 1 && reaction.count}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Reaction Button */}
                    <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 rounded-full"
                          >
                            <Sparkles className="h-3 w-3 text-muted-foreground" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="p-1 flex gap-1"
                        >
                          {reactionOptions.map((reaction) => (
                            <TooltipProvider key={reaction.emoji}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 rounded-full"
                                    onClick={() =>
                                      addReaction(message.id, reaction.emoji)
                                    }
                                  >
                                    {reaction.emoji}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent
                                  side="bottom"
                                  className="py-1 px-2 text-xs"
                                >
                                  {reaction.label}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>

                {message.sender === "user" && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      You
                    </AvatarFallback>
                  </Avatar>
                )}
              </motion.div>
            ))}

            {/* Typing indicator */}
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-start gap-3"
              >
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarImage src="/bot-avatar.png" alt="Forever Assistant" />
                  <AvatarFallback className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>

                <div className="flex flex-col max-w-[80%] items-start">
                  <div className="bg-muted rounded-2xl px-4 py-2.5 border border-border">
                    <div className="flex space-x-1 h-5 items-center">
                      <div
                        className="h-2 w-2 rounded-full bg-indigo-500/70 animate-bounce"
                        style={{ animationDelay: "0ms" }}
                      ></div>
                      <div
                        className="h-2 w-2 rounded-full bg-indigo-500/70 animate-bounce"
                        style={{ animationDelay: "150ms" }}
                      ></div>
                      <div
                        className="h-2 w-2 rounded-full bg-indigo-500/70 animate-bounce"
                        style={{ animationDelay: "300ms" }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-xs text-muted-foreground mt-1 px-2">
                    Assistant is typing...
                  </span>
                </div>
              </motion.div>
            )}

            {/* Invisible element to scroll to */}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="border-t p-2 bg-muted/20 flex flex-wrap gap-2">
          {attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="relative group bg-background rounded-md border p-2 flex items-center gap-2 text-sm"
            >
              {attachment.type === "image" ? (
                <img
                  src={attachment.url}
                  alt={attachment.name}
                  className="w-10 h-10 object-cover rounded"
                />
              ) : (
                <div className="p-2 rounded-full bg-primary/10">
                  <File className="h-4 w-4 text-primary" />
                </div>
              )}
              <div className="max-w-[120px]">
                <div className="font-medium truncate text-xs">
                  {attachment.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {attachment.size
                    ? `${Math.round(attachment.size / 1024)} KB`
                    : ""}
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full absolute -top-2 -right-2 bg-background border shadow-sm opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeAttachment(attachment.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Voice Recording UI */}
      {isRecording && (
        <div className="border-t p-3 bg-muted/20 flex items-center gap-3">
          <div className="p-2 rounded-full bg-red-500 animate-pulse">
            <Mic className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium">
                Recording voice message...
              </span>
              <span className="text-xs text-muted-foreground">
                {Math.floor(recordingProgress / 10)}s
              </span>
            </div>
            <Progress value={recordingProgress} className="h-1.5" />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full hover:bg-red-100 hover:text-red-500"
            onClick={toggleVoiceRecording}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Input Form */}
      <form
        onSubmit={handleSubmit}
        className="border-t p-2 sm:p-3 flex items-center gap-1 sm:gap-2 bg-muted/30"
      >
        {/* Search Button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 sm:h-9 sm:w-9 rounded-full"
                onClick={toggleSearch}
              >
                <Search className="h-4 w-4 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Search in conversation</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Attachment Button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 sm:h-9 sm:w-9 rounded-full"
                onClick={handleFileAttachment}
              >
                <Paperclip className="h-4 w-4 text-muted-foreground" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Attach file</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Hidden File Input */}
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileUpload}
          multiple
        />

        {/* Message Input */}
        <Input
          ref={inputRef}
          type="text"
          placeholder="Type your message..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className="flex-1 bg-background border-muted-foreground/20 focus-visible:ring-indigo-500"
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              handleSubmit(e);
            }
          }}
        />

        {/* Voice Input Button (conditional) */}
        {voiceInputEnabled && !inputValue.trim() && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant={isRecording ? "destructive" : "ghost"}
                  size="icon"
                  className={cn(
                    "h-8 w-8 sm:h-9 sm:w-9 rounded-full",
                    isRecording && "animate-pulse"
                  )}
                  onClick={toggleVoiceRecording}
                >
                  <Mic className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>{isRecording ? "Stop recording" : "Voice message"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Send Button */}
        <Button
          type="submit"
          size="icon"
          disabled={
            !inputValue.trim() && attachments.length === 0 && !isRecording
          }
          className={cn(
            "h-8 w-8 sm:h-9 sm:w-9 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 transition-all duration-300",
            !inputValue.trim() &&
              attachments.length === 0 &&
              !isRecording &&
              "opacity-50"
          )}
        >
          <Send className="h-4 w-4" />
        </Button>
      </form>
    </div>
  );
};

export default ChatTab;
