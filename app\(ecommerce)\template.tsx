"use client";

import { AnimatePresence, motion } from "framer-motion";

export default function Template({ children }: { children: React.ReactNode }) {
  return (
    <AnimatePresence mode="wait" initial={true}>
      <motion.div
        initial={{
          y: 50,
          opacity: 0,
          scale: 0.98
        }}
        animate={{
          y: 0,
          opacity: 1,
          scale: 1
        }}
        exit={{
          y: 30,
          opacity: 0,
          scale: 0.96
        }}
        transition={{
          type: "spring",
          stiffness: 100,
          damping: 20,
          mass: 0.8
        }}
        className="page-transition-wrapper"
      >
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            delay: 0.2,
            duration: 0.4
          }}
        >
          {children}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
