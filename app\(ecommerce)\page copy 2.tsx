"use client";
import InView from "@/components/InView";
import Logo from "@/components/Logo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { product } from "@/data/product";
import { <PERSON>R<PERSON>, Heart, ShoppingBag, Star } from "lucide-react";
import Link from "next/link";
import React from "react";
import { motion } from "framer-motion";

function Page() {
  return (
    <div className="">
      <div className="relative overflow-hidden bg-gradient-to-b from-white to-gray-50 min-h-[calc(100vh-72px)]">
        {/* Background pattern elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -right-40 -top-40 h-[500px] w-[500px] rounded-full border border-gray-200 opacity-20"></div>
          <div className="absolute -bottom-40 -left-40 h-[600px] w-[600px] rounded-full border border-gray-200 opacity-20"></div>
          <div className="absolute right-1/4 top-1/3 h-[300px] w-[300px] rounded-full border border-gray-200 opacity-20"></div>
        </div>

        <main className="container relative mx-auto px-4 py-12 flex flex-col md:flex-row items-center min-h-[calc(100vh-72px)]">
          <div className="w-full md:w-1/2 md:pr-12 mb-12 md:mb-0 z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="space-y-6"
            >
              <div className="inline-block">
                <span className="inline-flex items-center gap-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-black text-white">
                  New Collection{" "}
                  <span className="relative flex h-2 w-2">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-white"></span>
                  </span>
                </span>
              </div>

              <h1 className="text-5xl md:text-7xl font-bold tracking-tight">
                <span className="block">AirNags®</span>
                <span className="block text-black/75">Premium Quality</span>
              </h1>

              <p className="text-lg md:text-xl text-gray-600 max-w-md">
                Keep your everyday style chic and on-trend with our carefully
                curated collection of 20+ premium styles designed for your
                comfort.
              </p>

              <div className="flex flex-wrap gap-4 pt-4">
                <Button className="rounded-full h-14 px-8 bg-black hover:bg-black/90 text-white shadow-lg hover:shadow-xl transition-all">
                  Shop Collection
                </Button>
                <Button
                  variant="outline"
                  className="rounded-full h-14 px-8 border-gray-300 hover:border-black hover:bg-black/5 transition-all flex items-center gap-2"
                >
                  Explore More
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-8 pt-6">
                <div className="flex -space-x-3">
                  {[1, 2, 3, 4].map((item) => (
                    <div
                      key={item}
                      className="h-10 w-10 rounded-full border-2 border-white overflow-hidden"
                    >
                      <img
                        src={`https://randomuser.me/api/portraits/men/${item + 10
                          }.jpg`}
                        alt="Customer"
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ))}
                  <div className="h-10 w-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs font-medium">
                    +2k
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((item) => (
                      <Star
                        key={item}
                        className="h-4 w-4 fill-amber-400 text-amber-400"
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    From 10k+ happy customers
                  </span>
                </div>
              </div>
            </motion.div>
          </div>          <div className="w-full md:w-1/2 flex justify-center z-10">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
              className="relative h-[500px] w-full"
            >
              {/* Gradient background effect */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-pink-600 to-purple-600 opacity-20 blur-[100px] rounded-full"></div>

              {/* Main floating video */}
              <motion.div
                initial={{ y: 20, rotate: -2 }}
                animate={{ y: [0, -10, 0], rotate: 0 }}
                transition={{
                  y: { repeat: Infinity, duration: 4, ease: "easeInOut" },
                  rotate: { duration: 1 }
                }}
                className="absolute top-0 left-0 md:left-10 lg:left-20 w-[280px] h-[190px] sm:w-[320px] sm:h-[240px] rounded-xl overflow-hidden shadow-2xl border-2 border-white/20"
                style={{ zIndex: 20 }}
              >
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  loop
                  muted
                  playsInline
                >
                  <source src="https://media.useclip.com/campaigns/campaign-8010/clip_creator_9642_e8022668-7bb2-40b3-a738-bf13682953c8.mov" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </motion.div>

              {/* Secondary floating video */}
              <motion.div
                initial={{ y: -10, rotate: 3 }}
                animate={{ y: [0, 15, 0], rotate: 0 }}
                transition={{
                  y: { repeat: Infinity, duration: 5, ease: "easeInOut" },
                  rotate: { duration: 1 }
                }}
                className="absolute bottom-0 right-0 md:right-10 w-[220px] h-[150px] sm:w-[280px] sm:h-[190px] rounded-xl overflow-hidden shadow-2xl border-2 border-white/20"
                style={{ zIndex: 10 }}
              >
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  loop
                  muted
                  playsInline
                >
                  <source src="https://media.useclip.com/campaigns/campaign-4502/clip_creator_15691_40d5716e-574d-49b4-bbbb-2f3439a32f42.mov" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </motion.div>

              {/* Statistics card */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
                className="absolute top-1/2 right-10 transform -translate-y-1/2 bg-white rounded-2xl shadow-2xl p-5 backdrop-blur-sm bg-white/90 border border-white/40 w-[220px]"
                style={{ zIndex: 30 }}
              >
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Performance Stats</h3>

                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">Sales</span>
                        <span className="text-sm font-bold text-emerald-600">+24%</span>
                      </div>
                      <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div className="h-full w-[72%] bg-emerald-500 rounded-full"></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">Visitors</span>
                        <span className="text-sm font-bold text-blue-600">+18%</span>
                      </div>
                      <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div className="h-full w-[64%] bg-blue-500 rounded-full"></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">Conversion</span>
                        <span className="text-sm font-bold text-purple-600">+12%</span>
                      </div>
                      <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div className="h-full w-[56%] bg-purple-500 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Badge floating element */}
              <motion.div
                initial={{ opacity: 0, y: -20, x: -20 }}
                animate={{ opacity: 1, y: 0, x: 0 }}
                transition={{ delay: 0.8, duration: 0.5 }}
                className="absolute left-10 bottom-20 bg-black text-white rounded-full shadow-lg px-4 py-2 text-sm font-medium"
                style={{ zIndex: 25 }}
              >
                <div className="flex items-center gap-2">
                  <span className="relative flex h-3 w-3">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                  </span>
                  <span>Trending Now</span>
                </div>              </motion.div>
            </motion.div>
          </div>
      </div>
    </motion.div>
            </motion.div >
          </div >
        </main >
      </div >
      <div className="mx-auto container py-16 px-4 relative">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(100,100,250,0.05),transparent_70%)]"></div>
        <div className="container mx-auto relative z-10">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-14">
            <div className="max-w-xl">
              <div className="inline-flex items-center gap-2 mb-2 bg-indigo-50 px-3 py-1 rounded-full">
                <span className="h-2 w-2 rounded-full bg-indigo-600 animate-pulse"></span>
                <Badge
                  variant="outline"
                  className="text-indigo-600 border-none bg-transparent"
                >
                  Explore Our Premium Collection
                </Badge>
              </div>
              <h3 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-indigo-900 to-gray-700">
                Browse By Categories
              </h3>
              <p className="text-gray-500 mt-4 max-w-md">
                Discover our meticulously curated selection designed to match
                your unique style and performance needs.
              </p>
            </div>
            <Button
              variant="outline"
              className="group mt-6 md:mt-0 h-12 px-6 rounded-full gap-2 border-gray-300 border-2 hover:border-black hover:bg-black hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
            >
              View All Categories
              <span className="relative w-5 h-5 overflow-hidden inline-flex items-center justify-center">
                <ArrowRight className="h-4 w-4 absolute group-hover:translate-x-8 group-hover:opacity-0 transition-all duration-300" />
                <ArrowRight className="h-4 w-4 absolute -translate-x-8 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-300" />
              </span>
            </Button>
          </div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 relative"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
            variants={{
              hidden: {},
              visible: {
                transition: {
                  staggerChildren: 0.15,
                },
              },
            }}
          >
            {/* Running Category */}
            <Link href="/categories/running-shoes">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-indigo-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M17.5 5.5C17.5 3.01472 15.4853 1 13 1C10.5147 1 8.5 3.01472 8.5 5.5V6.5H17.5V5.5Z"
                            stroke="#4F46E5"
                            strokeWidth="2"
                          />
                          <path
                            d="M4.5 6.5H19.5L18 23H6L4.5 6.5Z"
                            stroke="#4F46E5"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                      <span className="text-xs font-medium text-indigo-700 uppercase tracking-wider">
                        Performance
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-indigo-700 transition-colors duration-300">
                      Running Shoes
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Premium performance shoes engineered for speed and
                      endurance
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/656.png";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-indigo-100 flex items-center justify-center text-xs font-medium text-indigo-600">
                          +8
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-indigo-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        24 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-indigo-100 rounded-full overflow-hidden">
                      <div className="h-full w-2/3 bg-indigo-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/656.png"
                    alt="Running Shoes"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-indigo-600 hover:bg-indigo-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>

            {/* Casual Category */}
            <Link href="/categories/casual-sneakers">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-rose-500/20 to-pink-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-rose-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-rose-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3 18V6C3 3.79086 4.79086 2 7 2H17C19.2091 2 21 3.79086 21 6V18C21 20.2091 19.2091 22 17 22H7C4.79086 22 3 20.2091 3 18Z"
                            stroke="#E11D48"
                            strokeWidth="2"
                          />
                          <path
                            d="M15 9.5C15 11.7091 13.2091 13.5 11 13.5C8.79086 13.5 7 11.7091 7 9.5C7 7.29086 8.79086 5.5 11 5.5C13.2091 5.5 15 7.29086 15 9.5Z"
                            stroke="#E11D48"
                            strokeWidth="2"
                          />
                          <path
                            d="M17.5 20C17.1667 16.8333 15 13.5 11 13.5C7 13.5 4.83333 16.8333 4.5 20"
                            stroke="#E11D48"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-rose-700 uppercase tracking-wider">
                          Casual
                        </span>
                        <span className="text-[10px] font-semibold text-white bg-rose-500 px-1.5 py-0.5 rounded-sm">
                          NEW
                        </span>
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-rose-600 transition-colors duration-300">
                      Casual Sneakers
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Stylish comfort for everyday wear with premium materials
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/651.webp";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-rose-100 flex items-center justify-center text-xs font-medium text-rose-600">
                          +12
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-rose-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        36 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-rose-100 rounded-full overflow-hidden">
                      <div className="h-full w-[85%] bg-rose-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/651.webp"
                    alt="Casual Sneakers"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-rose-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-rose-600 hover:bg-rose-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>

            {/* Basketball Category */}
            <Link href="/categories/basketball-shoes">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 to-yellow-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-amber-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="#B45309"
                            strokeWidth="2"
                          />
                          <path
                            d="M12 22C12 17.5 16.5 12 16.5 7"
                            stroke="#B45309"
                            strokeWidth="2"
                          />
                          <path
                            d="M12 22C12 17.5 7.5 12 7.5 7"
                            stroke="#B45309"
                            strokeWidth="2"
                          />
                          <path d="M2 12H22" stroke="#B45309" strokeWidth="2" />
                        </svg>
                      </div>
                      <span className="text-xs font-medium text-amber-700 uppercase tracking-wider">
                        Sports
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-amber-700 transition-colors duration-300">
                      Basketball Shoes
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Court-ready performance with enhanced grip and support
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/822.png";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-amber-100 flex items-center justify-center text-xs font-medium text-amber-600">
                          +5
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-amber-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        18 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-amber-100 rounded-full overflow-hidden">
                      <div className="h-full w-[45%] bg-amber-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/822.png"
                    alt="Basketball Shoes"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-amber-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-amber-600 hover:bg-amber-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>

            {/* Training Category */}
            <Link href="/categories/training-shoes">
              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: { duration: 0.7, ease: "easeOut" },
                  },
                }}
                whileHover={{ y: -8, transition: { duration: 0.2 } }}
                className="group h-96 relative overflow-hidden rounded-3xl shadow-md hover:shadow-2xl transition-all duration-500 cursor-pointer"
              >
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-teal-600/30 opacity-90"></div>
                <div className="absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-emerald-600/20 to-transparent"></div>
                <div className="absolute inset-0 bg-[url('/texture.png')] opacity-5 mix-blend-overlay"></div>

                {/* Category Content */}
                <div className="absolute inset-0 p-8 flex flex-col justify-between z-10">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6 12H18"
                            stroke="#047857"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                          <path
                            d="M12 18V6"
                            stroke="#047857"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                          <path
                            d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                            stroke="#047857"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                      <span className="text-xs font-medium text-emerald-700 uppercase tracking-wider">
                        Training
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mt-4 group-hover:text-emerald-700 transition-colors duration-300">
                      Training Shoes
                    </h3>
                    <p className="text-gray-600 mt-2 text-sm max-w-xs">
                      Versatile support for intense workouts and cross-training
                    </p>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex -space-x-2">
                        {[1, 2, 3].map((i) => (
                          <div
                            key={i}
                            className="w-7 h-7 rounded-full border-2 border-white overflow-hidden"
                          >
                            <img
                              src={`/small-${i}.jpg`}
                              alt="Product thumbnail"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = "/564.webp";
                              }}
                            />
                          </div>
                        ))}
                        <div className="w-7 h-7 rounded-full border-2 border-white bg-emerald-100 flex items-center justify-center text-xs font-medium text-emerald-600">
                          +9
                        </div>
                      </div>
                      <span className="text-xs font-semibold bg-white/80 text-emerald-700 px-3 py-1 rounded-full backdrop-blur-sm">
                        29 Products
                      </span>
                    </div>

                    <div className="h-0.5 w-full bg-emerald-100 rounded-full overflow-hidden">
                      <div className="h-full w-[75%] bg-emerald-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Floating shoe image */}
                <div className="absolute -right-10 top-1/2 -translate-y-1/4 group-hover:translate-y-[-30%] transition-transform duration-700">
                  <img
                    src="/564.webp"
                    alt="Training Shoes"
                    className="h-48 w-48 object-contain transform -rotate-12 group-hover:rotate-0 transition-all duration-700 drop-shadow-2xl"
                  />
                </div>

                {/* Hover overlay with button */}
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-8">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="rounded-full bg-white/90 backdrop-blur-sm text-emerald-600 hover:bg-emerald-600 hover:text-white transition-colors"
                  >
                    <span>Explore Collection</span>
                    <ArrowRight className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </div>
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="flex items-center justify-between mb-8">
          <h3 className="text-3xl font-bold">Latest Arrivals</h3>
          <Button variant="outline" className="gap-2 border-gray-300">
            View All Products <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-8">
          {product.map((item) => (
            <Link
              key={item.name}
              href={`/products/${item.name.replaceAll(" ", "-").toLowerCase()}`}
              className="group bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:translate-y-[-4px] cursor-pointer border border-gray-100"
            >
              <InView>
                <div className="relative overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-72 object-cover object-center transition-all duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute top-3 left-3 flex gap-2">
                    <span className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg">
                      NEW
                    </span>
                    <span className="bg-red-500 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg">
                      -20%
                    </span>
                  </div>
                  <div className="absolute right-3 top-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <button className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors">
                      <Heart className="h-4 w-4 text-gray-700" />
                    </button>
                    <button className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors">
                      <ShoppingBag className="h-4 w-4 text-gray-700" />
                    </button>
                  </div>
                </div>
                <div className="p-5">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className="h-4 w-4 text-amber-400 fill-amber-400"
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500 font-medium">
                      120 reviews
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-1 group-hover:text-indigo-600 transition-colors">
                    {item.name}
                  </h3>
                  <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                    Premium quality skincare product for all skin types
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-gray-900">
                        {item.price} MAD
                      </span>
                      <span className="text-sm text-gray-400 line-through">
                        130.00 MAD
                      </span>
                    </div>
                    <div className="bg-indigo-50 text-indigo-600 text-xs font-medium px-2 py-1 rounded">
                      In Stock
                    </div>
                  </div>
                </div>
              </InView>
            </Link>
          ))}
        </div>
        <div className="py-6 flex items-center justify-center">
          <Button variant={"default"} className="rounded-full group gap-1">
            Load More{" "}
            <ArrowRight className="h-4 w-4 group-hover:translate-x-2 transition-transform" />
          </Button>
        </div>
      </div>
      <div className="container mx-auto px-4 py-16">
        <div className="relative rounded-3xl overflow-hidden">
          {/* Background elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-50 via-gray-50 to-pink-50"></div>
          <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-b from-pink-200 to-indigo-200 rounded-full filter blur-3xl opacity-20 -translate-y-1/2 translate-x-1/3"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-t from-indigo-200 to-purple-200 rounded-full filter blur-3xl opacity-20 translate-y-1/2 -translate-x-1/3"></div>

          <div className="relative z-10 p-8 md:p-12 lg:p-16 flex flex-col lg:flex-row items-center justify-between">
            <div className="mb-10 lg:mb-0 lg:mr-8 max-w-md">
              <div className="inline-flex items-center gap-2 mb-4 px-3 py-1 bg-black/5 rounded-full">
                <span className="flex h-2 w-2 rounded-full bg-indigo-600 animate-pulse"></span>
                <span className="text-xs font-medium text-gray-700">
                  Join 25,000+ subscribers
                </span>
              </div>

              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-indigo-800 to-gray-800">
                Stay in the loop.
              </h2>

              <p className="text-lg sm:text-xl text-gray-600 mb-8">
                Get exclusive access to limited drops, special offers and
                insider news about upcoming releases.
              </p>

              <motion.form
                className="space-y-5 w-full max-w-md"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex flex-col space-y-2">
                  <label
                    htmlFor="subscriber-email"
                    className="text-sm font-medium text-gray-700"
                  >
                    Email address
                  </label>
                  <div className="relative">
                    <input
                      id="subscriber-email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-20 transition-all duration-200"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="text-gray-400"
                      >
                        <path
                          d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C19.5304 19 20.0391 18.7893 20.4142 18.4142C20.7893 18.0391 21 17.5304 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3.96086 5.21071 3.58579 5.58579C3.21071 5.96086 3 6.46957 3 7V17C3 18.1046 3.89543 19 5 19Z"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <div>
                  <Button className="w-full bg-black hover:bg-gray-800 text-white font-medium rounded-xl py-6 transition-all duration-300 shadow-sm hover:shadow-md">
                    Subscribe to Newsletter
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-2"
                    >
                      <path
                        d="M5 12H19M19 12L12 5M19 12L12 19"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="marketing-consent"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="marketing-consent"
                    className="text-sm text-gray-600"
                  >
                    I agree to receive emails about new collections and
                    exclusive offers.
                  </label>
                </div>

                <div className="flex items-center gap-4 pt-2">
                  <div className="flex -space-x-2">
                    {[1, 2, 3, 4].map((i) => (
                      <div
                        key={i}
                        className="h-6 w-6 rounded-full border-2 border-white overflow-hidden"
                      >
                        <img
                          src={`https://randomuser.me/api/portraits/${
                            i % 2 === 0 ? "women" : "men"
                          }/${i + 10}.jpg`}
                          alt="Subscriber"
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500">
                    Join thousands of happy subscribers
                  </p>
                </div>
              </motion.form>
            </div>

            <div className="relative w-full max-w-sm lg:max-w-md xl:max-w-lg">
              <motion.div
                initial={{ opacity: 0, scale: 0.9, rotate: 5 }}
                whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="relative z-10"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-pink-500/30 rounded-3xl transform rotate-3 scale-105 blur-xl opacity-40"></div>
                <img
                  src="/955.png"
                  alt="Premium shoes"
                  className="relative z-10 w-full h-auto max-h-80 object-contain transform -rotate-6 hover:rotate-0 transition-transform duration-700 drop-shadow-xl"
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20, y: 20 }}
                whileInView={{ opacity: 1, x: 0, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="absolute bottom-4 -left-4 bg-white rounded-xl shadow-xl p-3 max-w-[180px] z-20 border border-gray-100"
              >
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21 5L11 5M21 12L8 12M21 19L14 19"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Exclusive Content</h4>
                    <p className="text-xs text-gray-500">
                      For subscribers only
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20, y: -20 }}
                whileInView={{ opacity: 1, x: 0, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="absolute top-10 -right-4 bg-white rounded-xl shadow-xl p-3 max-w-[180px] z-20 border border-gray-100"
              >
                <div className="text-center">
                  <div className="inline-block mb-1 bg-indigo-100 text-indigo-700 text-xs px-2 py-1 rounded-full">
                    Weekly Updates
                  </div>
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <span className="text-amber-500">★★★★★</span>
                    <span className="text-xs font-medium">4.9/5</span>
                  </div>
                  <p className="text-xs text-gray-500">Rated by subscribers</p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default Page;
