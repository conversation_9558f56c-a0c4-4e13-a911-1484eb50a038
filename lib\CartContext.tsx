"use client";

import React, { createContext, useContext, useState, useEffect } from "react";

export interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  name: string;
  image?: string;
  size?: string;
  color?: string;
}

export interface WishlistItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  image?: string;
  dateAdded: string;
}

export interface SavedForLaterItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  name: string;
  image?: string;
  size?: string;
  color?: string;
  dateAdded: string;
}

export interface Coupon {
  code: string;
  discountType: "percentage" | "fixed";
  discountValue: number;
  minOrderValue?: number;
  expiryDate?: string;
  isApplied: boolean;
}

interface CartContextType {
  // Cart Items
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;

  // Cart Totals
  subtotal: number;
  total: number;
  itemCount: number;

  // Wishlist
  wishlist: WishlistItem[];
  addToWishlist: (item: WishlistItem) => void;
  removeFromWishlist: (id: string) => void;
  moveToWishlist: (itemId: string) => void;

  // Saved For Later
  savedForLater: SavedForLaterItem[];
  addToSavedForLater: (item: SavedForLaterItem) => void;
  removeFromSavedForLater: (id: string) => void;
  moveToSavedForLater: (itemId: string) => void;
  moveToCart: (itemId: string) => void;

  // Coupons
  coupons: Coupon[];
  applyCoupon: (code: string) => boolean;
  removeCoupon: (code: string) => void;

  // Shipping
  shippingCost: number;
  freeShippingThreshold: number;
  remainingForFreeShipping: number;

  // Tax
  taxRate: number;
  taxAmount: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

// Helper function to generate a unique ID
const generateId = (): string => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

export function CartProvider({ children }: { children: React.ReactNode }) {
  // Initialize state from localStorage if available
  const [items, setItems] = useState<CartItem[]>(() => {
    if (typeof window !== "undefined") {
      const savedItems = localStorage.getItem("cartItems");
      return savedItems ? JSON.parse(savedItems) : [];
    }
    return [];
  });

  const [wishlist, setWishlist] = useState<WishlistItem[]>(() => {
    if (typeof window !== "undefined") {
      const savedWishlist = localStorage.getItem("wishlistItems");
      return savedWishlist ? JSON.parse(savedWishlist) : [];
    }
    return [];
  });

  const [savedForLater, setSavedForLater] = useState<SavedForLaterItem[]>(
    () => {
      if (typeof window !== "undefined") {
        const savedItems = localStorage.getItem("savedForLaterItems");
        return savedItems ? JSON.parse(savedItems) : [];
      }
      return [];
    }
  );

  const [coupons, setCoupons] = useState<Coupon[]>(() => {
    if (typeof window !== "undefined") {
      const savedCoupons = localStorage.getItem("coupons");
      return savedCoupons ? JSON.parse(savedCoupons) : [];
    }
    return [];
  });

  // Constants
  const freeShippingThreshold = 100;
  const taxRate = 0.07; // 7% tax rate

  // Persist state to localStorage when it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("cartItems", JSON.stringify(items));
      localStorage.setItem("wishlistItems", JSON.stringify(wishlist));
      localStorage.setItem("savedForLaterItems", JSON.stringify(savedForLater));
      localStorage.setItem("coupons", JSON.stringify(coupons));
    }
  }, [items, wishlist, savedForLater, coupons]);

  // Cart Item Functions
  const addItem = (newItem: CartItem) => {
    setItems((currentItems) => {
      // Check if the item already exists with the same ID, size, and color
      const existingItemIndex = currentItems.findIndex(
        (item) =>
          item.productId === newItem.productId &&
          item.size === newItem.size &&
          item.color === newItem.color
      );

      if (existingItemIndex >= 0) {
        // Update quantity of existing item
        const updatedItems = [...currentItems];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + newItem.quantity,
        };
        return updatedItems;
      } else {
        // Add new item with a unique ID
        return [
          ...currentItems,
          {
            ...newItem,
            id: newItem.id || generateId(),
          },
        ];
      }
    });
  };

  const removeItem = (id: string) => {
    setItems((currentItems) => currentItems.filter((item) => item.id !== id));
  };

  const updateQuantity = (id: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(id);
      return;
    }

    setItems((currentItems) =>
      currentItems.map((item) =>
        item.id === id ? { ...item, quantity } : item
      )
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  // Wishlist Functions
  const addToWishlist = (item: WishlistItem) => {
    setWishlist((currentWishlist) => {
      // Check if item already exists in wishlist
      if (
        currentWishlist.some(
          (wishItem) => wishItem.productId === item.productId
        )
      ) {
        return currentWishlist;
      }
      return [
        ...currentWishlist,
        {
          ...item,
          id: item.id || generateId(),
          dateAdded: item.dateAdded || new Date().toISOString(),
        },
      ];
    });
  };

  const removeFromWishlist = (id: string) => {
    setWishlist((currentWishlist) =>
      currentWishlist.filter((item) => item.id !== id)
    );
  };

  const moveToWishlist = (itemId: string) => {
    const itemToMove = items.find((item) => item.id === itemId);
    if (!itemToMove) return;

    // Add to wishlist
    addToWishlist({
      id: generateId(),
      productId: itemToMove.productId,
      name: itemToMove.name,
      price: itemToMove.price,
      image: itemToMove.image,
      dateAdded: new Date().toISOString(),
    });

    // Remove from cart
    removeItem(itemId);
  };

  // Saved For Later Functions
  const addToSavedForLater = (item: SavedForLaterItem) => {
    setSavedForLater((currentSaved) => {
      // Check if item already exists in saved for later
      const existingItemIndex = currentSaved.findIndex(
        (savedItem) =>
          savedItem.productId === item.productId &&
          savedItem.size === item.size &&
          savedItem.color === item.color
      );

      if (existingItemIndex >= 0) {
        // Update quantity of existing item
        const updatedItems = [...currentSaved];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + item.quantity,
        };
        return updatedItems;
      } else {
        // Add new item
        return [
          ...currentSaved,
          {
            ...item,
            id: item.id || generateId(),
            dateAdded: item.dateAdded || new Date().toISOString(),
          },
        ];
      }
    });
  };

  const removeFromSavedForLater = (id: string) => {
    setSavedForLater((currentSaved) =>
      currentSaved.filter((item) => item.id !== id)
    );
  };

  const moveToSavedForLater = (itemId: string) => {
    const itemToMove = items.find((item) => item.id === itemId);
    if (!itemToMove) return;

    // Add to saved for later
    addToSavedForLater({
      ...itemToMove,
      id: generateId(),
      dateAdded: new Date().toISOString(),
    });

    // Remove from cart
    removeItem(itemId);
  };

  const moveToCart = (itemId: string) => {
    const itemToMove = savedForLater.find((item) => item.id === itemId);
    if (!itemToMove) return;

    // Add to cart
    addItem({
      id: generateId(),
      productId: itemToMove.productId,
      name: itemToMove.name,
      price: itemToMove.price,
      quantity: itemToMove.quantity,
      image: itemToMove.image,
      size: itemToMove.size,
      color: itemToMove.color,
    });

    // Remove from saved for later
    removeFromSavedForLater(itemId);
  };

  // Coupon Functions
  const applyCoupon = (code: string): boolean => {
    // Check if coupon is already applied
    if (coupons.some((coupon) => coupon.code === code && coupon.isApplied)) {
      return false;
    }

    // In a real app, you would validate the coupon code against a database
    // For this example, we'll use some hardcoded valid coupons
    const validCoupons = [
      {
        code: "WELCOME10",
        discountType: "percentage",
        discountValue: 10,
        minOrderValue: 0,
      },
      {
        code: "SAVE20",
        discountType: "percentage",
        discountValue: 20,
        minOrderValue: 50,
      },
      {
        code: "FLAT15",
        discountType: "fixed",
        discountValue: 15,
        minOrderValue: 100,
      },
    ];

    const foundCoupon = validCoupons.find((coupon) => coupon.code === code);

    if (foundCoupon) {
      // Check if minimum order value is met
      if (subtotal < foundCoupon.minOrderValue) {
        return false;
      }

      setCoupons([
        ...coupons,
        {
          ...foundCoupon,
          isApplied: true,
          expiryDate: new Date(
            Date.now() + 7 * 24 * 60 * 60 * 1000
          ).toISOString(), // 7 days from now
        } as Coupon,
      ]);

      return true;
    }

    return false;
  };

  const removeCoupon = (code: string) => {
    setCoupons((currentCoupons) =>
      currentCoupons.filter((coupon) => coupon.code !== code)
    );
  };

  // Calculate cart totals
  const subtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const itemCount = items.reduce((count, item) => count + item.quantity, 0);

  // Calculate shipping cost
  const shippingCost = subtotal >= freeShippingThreshold ? 0 : 15;
  const remainingForFreeShipping = Math.max(
    0,
    freeShippingThreshold - subtotal
  );

  // Calculate discount from coupons
  const calculateDiscount = () => {
    if (coupons.length === 0) return 0;

    // For simplicity, we'll just apply the highest discount
    let maxDiscount = 0;

    coupons.forEach((coupon) => {
      if (coupon.isApplied) {
        let discount = 0;

        if (coupon.discountType === "percentage") {
          discount = subtotal * (coupon.discountValue / 100);
        } else if (coupon.discountType === "fixed") {
          discount = Math.min(subtotal, coupon.discountValue); // Don't discount more than the subtotal
        }

        maxDiscount = Math.max(maxDiscount, discount);
      }
    });

    return maxDiscount;
  };

  const discount = calculateDiscount();

  // Calculate tax
  const taxAmount = (subtotal - discount) * taxRate;

  // Calculate total
  const total = subtotal - discount + shippingCost + taxAmount;

  return (
    <CartContext.Provider
      value={{
        // Cart Items
        items,
        addItem,
        removeItem,
        updateQuantity,
        clearCart,

        // Cart Totals
        subtotal,
        total,
        itemCount,

        // Wishlist
        wishlist,
        addToWishlist,
        removeFromWishlist,
        moveToWishlist,

        // Saved For Later
        savedForLater,
        addToSavedForLater,
        removeFromSavedForLater,
        moveToSavedForLater,
        moveToCart,

        // Coupons
        coupons,
        applyCoupon,
        removeCoupon,

        // Shipping
        shippingCost,
        freeShippingThreshold,
        remainingForFreeShipping,

        // Tax
        taxRate,
        taxAmount,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
}
