import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { CartProvider } from "@/lib/CartContext";
import { AuthProvider } from "@/lib/auth-context";

export const metadata: Metadata = {
  title: "Forever | Take care of yourself",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="antialiased">
        <AuthProvider>
          <CartProvider>{children}</CartProvider>
        </AuthProvider>
      </body>
      <script
        defer
        src="https://analytics.daddasoft.com/script.js"
        data-website-id="7a7dd6e6-9e7e-4e97-8620-f5ec7562863a"
      ></script>
    </html>
  );
}
